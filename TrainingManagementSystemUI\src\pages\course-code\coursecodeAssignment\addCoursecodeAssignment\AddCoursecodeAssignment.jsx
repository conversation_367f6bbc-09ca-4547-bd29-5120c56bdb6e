import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './AddCoursecodeAssignment.module.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Select from 'react-select';
import { createCourseCodeAssignment, fetchApprovedCourseCodes, searchUsersBasicInfo } from '../../../../services/course-code/CoursecodeAssignmentService';


const AddCoursecodeAssignment = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    approvedCourseCode: '',
    remark: '',
  });

  // Approved course codes state
  const [approvedCourseCodes, setApprovedCourseCodes] = useState([]);
  const [selectedApprovedCourseCode, setSelectedApprovedCourseCode] = useState(null);

  // User selection state
  const [availableUsers, setAvailableUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [userSearchFilters, setUserSearchFilters] = useState({
    userName: '',
    designationName: '',
    roleName: ''
  });
  const [userLoading, setUserLoading] = useState(false);
  const [userPage, setUserPage] = useState(0);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  const [shouldRefreshUsers, setShouldRefreshUsers] = useState(false);
  const userListRef = useRef(null);
  const userSearchTimeoutRef = useRef(null);

  // Fetch approved course codes
  useEffect(() => {
    fetchApprovedCourseCodesData();
  }, []);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Refresh users when shouldRefreshUsers flag is set
  useEffect(() => {
    if (shouldRefreshUsers) {
      setUserPage(0);
      fetchUsers(userSearchFilters, 0, false);
      setShouldRefreshUsers(false);
    }
  }, [shouldRefreshUsers, selectedUsers]);

  // Fetch approved course codes
  const fetchApprovedCourseCodesData = async () => {
    try {
      const userID = sessionStorage.getItem('userID');
      const payload = {
        userID: userID ? parseInt(userID) : 0,
        page: {
          offset: 0,
          fetch: 100 // Fetch more items for dropdown
        },
        searchText: "",
        showOnlyUnderReview: true
      };

      const response = await fetchApprovedCourseCodes(payload);

      if (response?.header?.errorCount === 0) {
        // Format the response data for react-select
        const formattedCodes = response.coursecodes?.map(code => ({
          value: code.courseCode,
          label: `${code.courseTitle} (${code.courseCode})`,
          courseCodeID: code.courseCodeID,
          courseTitle: code.courseTitle
        })) || [];

        setApprovedCourseCodes(formattedCodes);
      } else {
        toast.error('Error loading approved course codes');
        setApprovedCourseCodes([]);
      }
    } catch (error) {
      toast.error('Error loading approved course codes');
      setApprovedCourseCodes([]);
    }
  };

  // Fetch users with search and pagination
  const fetchUsers = async (searchFilters = userSearchFilters, page = 0, append = false) => {
    try {
      setUserLoading(true);

      const userID = sessionStorage.getItem('userID');
      const itemsPerPage = 10;

      const payload = {
        userId: userID ? parseInt(userID) : 0,
        page: {
          offset: page * itemsPerPage,
          fetch: itemsPerPage
        },
        userSearchText: searchFilters.userName || "",
        designationSearchText: searchFilters.designationName || "",
        roleSearchText: searchFilters.roleName || ""
      };

      const response = await searchUsersBasicInfo(payload);

      if (response?.header?.errorCount === 0) {
        // Filter out already selected users
        const availableUsersFromAPI = response.usersBasicInfo?.filter(user =>
          !selectedUsers.some(selected => selected.userID === user.userID)
        ) || [];

        // Format user data to match expected structure
        const formattedUsers = availableUsersFromAPI.map(user => ({
          userID: user.userID,
          userName: `${user.firstName} ${user.lastName}`.trim(),
          departmentName: user.departmentName || 'N/A',
          roleName: user.roleName || 'N/A',
          designationName: user.designationName || 'N/A'
        }));

        setAvailableUsers(prev => append ? [...prev, ...formattedUsers] : formattedUsers);
        setHasMoreUsers(formattedUsers.length === itemsPerPage);
      } else {
        toast.error('Error loading users');
        setAvailableUsers(append ? availableUsers : []);
      }
    } catch (error) {
      toast.error('Error loading users');
      setAvailableUsers(append ? availableUsers : []);
    } finally {
      setUserLoading(false);
    }
  };

  // User interaction functions
  const handleAddUser = (user) => {
    setSelectedUsers(prev => [...prev, user]);
    setAvailableUsers(prev => prev.filter(u => u.userID !== user.userID));
  };

  const handleRemoveUser = (user) => {
    // Remove from selected users
    setSelectedUsers(prev => prev.filter(u => u.userID !== user.userID));

    // Trigger user refresh using useEffect
    setShouldRefreshUsers(true);
  };

  const handleUserSearchChange = (field, value) => {
    const newFilters = { ...userSearchFilters, [field]: value };
    setUserSearchFilters(newFilters);

    if (userSearchTimeoutRef.current) {
      clearTimeout(userSearchTimeoutRef.current);
    }

    userSearchTimeoutRef.current = setTimeout(() => {
      setUserPage(0);
      fetchUsers(newFilters, 0, false);
    }, 500);
  };

  const handleUserScroll = () => {
    if (userListRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = userListRef.current;
      if (scrollHeight - scrollTop <= clientHeight * 1.5 && !userLoading && hasMoreUsers) {
        const nextPage = userPage + 1;
        setUserPage(nextPage);
        fetchUsers(userSearchFilters, nextPage, true);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async () => {
    if (!selectedApprovedCourseCode || selectedUsers.length === 0) {
      toast.error('Please select an approved course code and at least one user.');
      return;
    }

    try {
      // Prepare API payload according to the specification
      const payload = {
        courseCodeID: selectedApprovedCourseCode.courseCodeID,
        assignedUserIDs: selectedUsers.map(user => user.userID),
        remarks: formData.remark || "",
        signatureDate: new Date().toISOString(), // Current date in ISO format
        electronicSignature: sessionStorage.getItem('userName') || "string"
      };

      const result = await createCourseCodeAssignment(payload);

      if (result?.header?.errorCount === 0) {
        const apiMessage = result.header.messages?.[0]?.messageText || 'Course code assignment created successfully';
        toast.success(apiMessage);
        setTimeout(() => {
          navigate('/course-code/course-code-assignment');
        }, 2000);
      } else {
        const errorMessage = result?.header?.messages?.[0]?.messageText || 'Failed to create course code assignment';
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMsg = error?.response?.data?.header?.messages?.[0]?.messageText || 'Error creating course code assignment';
      toast.error(errorMsg);
    }
  };

  const handleCancel = () => {
    navigate('/course-code/course-code-assignment');
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <div className={styles.container}>
        <form className={styles.form}>
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Course Code Assignment</h3>

            <div className={styles.row}>
              <label>Approved Course Code <span className={styles.required}>*</span></label>
              <Select
                options={approvedCourseCodes}
                value={selectedApprovedCourseCode}
                onChange={(selected) => {
                  setSelectedApprovedCourseCode(selected);
                  setFormData(prev => ({ ...prev, approvedCourseCode: selected ? selected.value : '' }));
                }}
                isClearable
                placeholder="-- Select Approved Course Code --"
                className={styles.reactSelect}
              />
            </div>

            <div className={styles.row}>
              <label>Remarks</label>
              <textarea
                name="remark"
                value={formData.remark}
                onChange={handleInputChange}
                maxLength={500}
                placeholder="Enter remarks (max 500 characters)"
              />
              <small>{formData.remark.length}/500 characters</small>
            </div>

            {/* User Selection Panel */}
            <div className={styles.row}>
              <label>User Selection <span className={styles.required}>*</span></label>
              <div className={styles.userSelectionPanel}>
                {/* Left Side - User Selection Grid (70%) */}
                <div className={styles.userGridColumn}>
                  <h4>Available Users</h4>

                  {/* Search Filters */}
                  <div className={styles.searchFilters}>
                    <input
                      type="text"
                      placeholder="Search by User Name"
                      value={userSearchFilters.userName}
                      onChange={(e) => handleUserSearchChange('userName', e.target.value)}
                      className={styles.searchInput}
                    />
                    <input
                      type="text"
                      placeholder="Search by Designation"
                      value={userSearchFilters.designationName}
                      onChange={(e) => handleUserSearchChange('designationName', e.target.value)}
                      className={styles.searchInput}
                    />
                    <input
                      type="text"
                      placeholder="Search by Role"
                      value={userSearchFilters.roleName}
                      onChange={(e) => handleUserSearchChange('roleName', e.target.value)}
                      className={styles.searchInput}
                    />
                  </div>

                  {/* User Grid */}
                  <div className={styles.userGrid}>
                    <div className={styles.userGridHeader}>
                      <div>Department Name</div>
                      <div>User Name</div>
                      <div>Designation</div>
                      <div>Role Name</div>
                      <div>Action</div>
                    </div>
                    <div
                      className={styles.userGridBody}
                      ref={userListRef}
                      onScroll={handleUserScroll}
                    >
                      {availableUsers.length > 0 ? (
                        availableUsers.map(user => (
                          <div key={user.userID} className={styles.userGridRow}>
                            <div>{user.departmentName}</div>
                            <div>{user.userName}</div>
                            <div>{user.designationName}</div>
                            <div>{user.roleName}</div>
                            <div>
                              <button
                                type="button"
                                className={styles.addUserBtn}
                                onClick={() => handleAddUser(user)}
                                title="Add User"
                              >
                                +
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className={styles.noUsers}>
                          {userLoading ? 'Loading users...' : 'No users available'}
                        </div>
                      )}
                      {userLoading && (
                        <div className={styles.loadingIndicator}>Loading more users...</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Divider */}
                <div className={styles.userSelectionDivider}></div>

                {/* Right Side - Selected Users List (30%) */}
                <div className={styles.selectedUsersColumn}>
                  <h4>Selected Users ({selectedUsers.length})</h4>
                  <div className={styles.selectedUsersList}>
                    {selectedUsers.length > 0 ? (
                      selectedUsers.map(user => (
                        <div key={user.userID} className={styles.selectedUserItem}>
                          <div className={styles.userInfo}>
                            <div className={styles.userName}>{user.userName}</div>
                            <div className={styles.userDesignation}>{user.designationName}</div>
                            <div className={styles.userRole}>{user.roleName}</div>
                          </div>
                          <button
                            type="button"
                            className={styles.removeUserBtn}
                            onClick={() => handleRemoveUser(user)}
                            title="Remove User"
                          >
                            ×
                          </button>
                        </div>
                      ))
                    ) : (
                      <div className={styles.noSelectedUsers}>No users selected yet</div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.submitRow}>
              <button type="button" className={styles.primaryBtn} onClick={handleSubmit}>Submit</button>
              <button type="button" onClick={handleCancel} className={styles.cancelBtn}>Cancel</button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default AddCoursecodeAssignment;
