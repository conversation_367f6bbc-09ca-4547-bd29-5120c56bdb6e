.container {
  display: flex;
  height: 100vh;
  width: 100vw;
  flex-wrap: wrap;
}

.left,
.right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left {
  background-color: #ffffff;
  padding: 1% 0;
}

.right {
  background-color: #ffffff;
}

.leftImage {
  width: 90%;
  height: 99%;
  object-fit: cover;
  border-radius: 16px;
}

/* --- FORM SECTION --- */
.formContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 400px;
  padding: 20px;
  box-sizing: border-box;
}

.logo {
  width: 120px;
  margin-bottom: 20px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
  text-align: left;
  color: #4d7d95;
  display: block;
}

.inputWrapper {
  position: relative;
  width: 100%;
}

.inputError {
  border-color: red;
}

.errorText {
  color: red;
  font-size: 12px;
  position: absolute;
  bottom: -18px; /* Space the error message below the input field */
  right: 0;
  margin-top: 0;
  padding-left: 10px;
  display: block;
  text-align: left;
}

.input {
  width: 100%;
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 14px;
  background-color: #fefefe;
  color: #085074;
}

.input::placeholder {
  font-size: 12px;
  color: #888;
}

.forgotPassword {
  margin-top: 15px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.forgotPassword a {
  text-decoration: none;
  color: #007bff;
  font-size: 14px;
}

.signInButton {
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  background-color: #0072ab;
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.signInButton:hover {
  background-color: #005d8a;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.signInButton:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  box-shadow: none;
}

/* Login attempts warning */
.attemptsWarning {
  width: 100%;
  text-align: center;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #e74c3c;
  font-size: 14px;
  font-weight: 500;
}

/* Reset password button */
.resetPasswordBtn {
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #0072ab;
  font-size: 16px;
  background-color: transparent;
  color: #0072ab;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resetPasswordBtn:hover {
  background-color: #f0f8ff;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.modalInputGroup {
  margin-bottom: 1.5rem;
}

.modalInputGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: black;
  font-weight: 500;
}

.modalInputGroup input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  color: black;
}

.modalInputGroup input::placeholder {
  color: #666;
}

.modalButtons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.submitBtn {
  background-color: #127C96;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.submitBtn:hover {
  background-color: #0F6A83;
}

.cancelBtn {
  background-color: #ccc;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.cancelBtn:hover {
  background-color: #999;
}

.closeModal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #666;
}

.resetSuccess {
  text-align: center;
  color: black;
  padding: 1rem;
}

.resetError {
  color: #dc3545;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #f8d7da;
  border-radius: 4px;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 20px 0;
}

.line {
  width: 25%;
  height: 1px;
  background-color: #ccc;
  border: none;
}

.divider span {
  margin: 0 10px;
  color: #888;
  font-size: 14px;
  white-space: nowrap;
}

.socialButton {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Google */
.google {
  background-color: white;
  color: #333;
  border: 1px solid #ccc;
}

.google:hover {
  background-color: #f1f1f1;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* Facebook */
.facebook {
  background-color: white;
  color: #4a4a4a;
  border: 1px solid #ccc;
}

.facebook:hover {
  background-color: #f1f7ff;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.signupText {
  margin-top: 15px;
  font-size: 14px;
  color: #727272;
}

.signupText a {
  color: #007bff;
  text-decoration: none;
}


.inputWrapper {
  position: relative;
}

.eyeIcon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 1rem;
  color: #333;
}

/* --- RESPONSIVE FOR SMALLER SCREENS --- */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    height: auto;
  }

  .left,
  .right {
    width: 100%;
    height: 50vh;
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .leftImage {
    width: 90%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .right {
    justify-content: center;
    align-items: center;
    overflow-y: auto;
  }

  .formContainer {
    width: 90%;
    max-width: 320px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .input,
  .signInButton,
  .socialButton {
    width: 100%;
  }

  .forgotPassword {
    width: 100%;
  }
  
  .modal {
    width: 95%;
    padding: 20px;
  }
}

.forgotPasswordBtn {
  background: none;
  border: none;
  color: #127C96;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  margin-top: 4px;
  padding: 0;
}

.forgotPasswordBtn:hover {
  color: #0F6A83;
  text-decoration: underline;
}

