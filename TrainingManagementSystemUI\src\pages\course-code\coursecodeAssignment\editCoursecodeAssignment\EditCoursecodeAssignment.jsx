import { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styles from './EditCoursecodeAssignment.module.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Select from 'react-select';
import { fetchApprovedCourseCodes, searchUsersBasicInfo, fetchCourseCodeAssignmentById, updateCourseCodeAssignment } from '../../../../services/course-code/CoursecodeAssignmentService';


const EditCoursecodeAssignment = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [formData, setFormData] = useState({
    courseCodeID: '',
    approvedCourseCode: '',
    remark: '',
  });

  // Approved course codes state
  const [approvedCourseCodes, setApprovedCourseCodes] = useState([]);
  const [selectedApprovedCourseCode, setSelectedApprovedCourseCode] = useState(null);

  // User selection state
  const [availableUsers, setAvailableUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [userSearchFilters, setUserSearchFilters] = useState({
    userName: '',
    designationName: '',
    roleName: ''
  });
  const [userLoading, setUserLoading] = useState(false);
  const [userPage, setUserPage] = useState(0);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  const [assignmentLoading, setAssignmentLoading] = useState(false);
  const [shouldRefreshUsers, setShouldRefreshUsers] = useState(false);
  const userListRef = useRef(null);
  const userSearchTimeoutRef = useRef(null);

  // Modal state for reason for change
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');
  const [originalAssignments, setOriginalAssignments] = useState([]);
  const [initialSelectedUsers, setInitialSelectedUsers] = useState([]);
  const initialFormRef = useRef(null);

  // Fetch assignment details from API
  const fetchAssignmentDetails = async () => {
    try {
      setAssignmentLoading(true);

      const response = await fetchCourseCodeAssignmentById(id);

      if (response?.header?.errorCount === 0) {
        const assignmentData = response.coursecode;

        // Set form data
        setFormData({
          courseCodeID: assignmentData.courseCodeID,
          approvedCourseCode: assignmentData.courseCode,
          remark: assignmentData.remarks || '',
        });

        // Set selected approved course code
        setSelectedApprovedCourseCode({
          value: assignmentData.courseCode,
          label: `${assignmentData.courseTitle} (${assignmentData.courseCode})`,
          courseCodeID: assignmentData.courseCodeID,
          courseTitle: assignmentData.courseTitle
        });

        // Store original assignments for tracking deletions
        setOriginalAssignments(assignmentData.assignments || []);

        // Format and set selected users from assignments
        if (assignmentData.assignments && assignmentData.assignments.length > 0) {
          // Fetch detailed user information to get designation
          await fetchSelectedUsersDetails(assignmentData.assignments);
        }

        // Store initial form data for change detection
        setTimeout(() => {
          initialFormRef.current = JSON.stringify({
            courseCodeID: assignmentData.courseCodeID,
            approvedCourseCode: assignmentData.courseCode,
            remark: assignmentData.remarks || '',
            selectedUsers: assignmentData.assignments || []
          });
        }, 100);
      } else {
        toast.error(`Failed to fetch assignment details: ${response?.header?.messages?.[0]?.messageText || 'Unknown error'}`);
        setTimeout(() => {
          navigate('/course-code/course-code-assignment');
        }, 3000);
      }
    } catch (error) {
      toast.error(`Error loading assignment details: ${error.message}`);
      setTimeout(() => {
        navigate('/course-code/course-code-assignment');
      }, 3000);
    } finally {
      setAssignmentLoading(false);
    }
  };

  // Fetch detailed user information for selected users to get designation
  const fetchSelectedUsersDetails = async (assignments) => {
    try {
      const userID = sessionStorage.getItem('userID');
      const payload = {
        userId: userID ? parseInt(userID) : 0,
        page: {
          offset: 0,
          fetch: 100 // Fetch enough to get all assigned users
        },
        userSearchText: "",
        designationSearchText: "",
        roleSearchText: ""
      };

      const response = await searchUsersBasicInfo(payload);

      if (response?.header?.errorCount === 0) {
        const allUsers = response.usersBasicInfo || [];

        // Match assigned users with detailed user info
        const formattedUsers = assignments.map(assignment => {
          const userDetails = allUsers.find(user => user.userID === assignment.assignedUserID);

          return {
            userID: assignment.assignedUserID,
            userName: `${assignment.firstName} ${assignment.lastName}`.trim(),
            departmentName: assignment.departmentName || 'N/A',
            roleName: assignment.roleName || 'N/A',
            designationName: userDetails?.designationName || 'N/A'
          };
        });

        setSelectedUsers(formattedUsers);
        setInitialSelectedUsers(formattedUsers); // Store initial selection
      } else {
        // Fallback to basic assignment data without designation
        const formattedUsers = assignments.map(assignment => ({
          userID: assignment.assignedUserID,
          userName: `${assignment.firstName} ${assignment.lastName}`.trim(),
          departmentName: assignment.departmentName || 'N/A',
          roleName: assignment.roleName || 'N/A',
          designationName: 'N/A'
        }));
        setSelectedUsers(formattedUsers);
        setInitialSelectedUsers(formattedUsers); // Store initial selection
      }
    } catch (error) {
      // Fallback to basic assignment data without designation
      const formattedUsers = assignments.map(assignment => ({
        userID: assignment.assignedUserID,
        userName: `${assignment.firstName} ${assignment.lastName}`.trim(),
        departmentName: assignment.departmentName || 'N/A',
        roleName: assignment.roleName || 'N/A',
        designationName: 'N/A'
      }));
      setSelectedUsers(formattedUsers);
      setInitialSelectedUsers(formattedUsers); // Store initial selection
    }
  };

  // Load course code assignment data from API
  useEffect(() => {
    if (id) {
      fetchAssignmentDetails();
    }
  }, [id, navigate]);

  // Fetch approved course codes
  useEffect(() => {
    fetchApprovedCourseCodesData();
  }, []);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Refresh users when shouldRefreshUsers flag is set
  useEffect(() => {
    if (shouldRefreshUsers) {
      setUserPage(0);
      fetchUsers(userSearchFilters, 0, false);
      setShouldRefreshUsers(false);
    }
  }, [shouldRefreshUsers, selectedUsers]);

  // Fetch approved course codes
  const fetchApprovedCourseCodesData = async () => {
    try {
      const userID = sessionStorage.getItem('userID');
      const payload = {
        userID: userID ? parseInt(userID) : 0,
        page: {
          offset: 0,
          fetch: 100 // Fetch more items for dropdown
        },
        searchText: "",
        showOnlyUnderReview: true
      };

      const response = await fetchApprovedCourseCodes(payload);

      if (response?.header?.errorCount === 0) {
        // Format the response data for react-select
        const formattedCodes = response.coursecodes?.map(code => ({
          value: code.courseCode,
          label: `${code.courseTitle} (${code.courseCode})`,
          courseCodeID: code.courseCodeID,
          courseTitle: code.courseTitle
        })) || [];

        setApprovedCourseCodes(formattedCodes);
      } else {
        console.error('API Error:', response?.header?.messages);
        toast.error('Error loading approved course codes');
        // Fallback to empty array
        setApprovedCourseCodes([]);
      }
    } catch (error) {
      console.error('Error fetching approved course codes:', error);
      toast.error('Error loading approved course codes');
      setApprovedCourseCodes([]);
    }
  };

  // Fetch users with search and pagination
  const fetchUsers = async (searchFilters = userSearchFilters, page = 0, append = false) => {
    try {
      setUserLoading(true);

      const userID = sessionStorage.getItem('userID');
      const itemsPerPage = 10;

      const payload = {
        userId: userID ? parseInt(userID) : 0,
        page: {
          offset: page * itemsPerPage,
          fetch: itemsPerPage
        },
        userSearchText: searchFilters.userName || "",
        designationSearchText: searchFilters.designationName || "",
        roleSearchText: searchFilters.roleName || ""
      };

      const response = await searchUsersBasicInfo(payload);

      if (response?.header?.errorCount === 0) {
        // Filter out already selected users
        const availableUsersFromAPI = response.usersBasicInfo?.filter(user =>
          !selectedUsers.some(selected => selected.userID === user.userID)
        ) || [];

        // Format user data to match expected structure
        const formattedUsers = availableUsersFromAPI.map(user => ({
          userID: user.userID,
          userName: `${user.firstName} ${user.lastName}`.trim(),
          departmentName: user.departmentName || 'N/A',
          roleName: user.roleName || 'N/A',
          designationName: user.designationName || 'N/A'
        }));

        setAvailableUsers(prev => append ? [...prev, ...formattedUsers] : formattedUsers);
        setHasMoreUsers(formattedUsers.length === itemsPerPage);
      } else {
        console.error('API Error:', response?.header?.messages);
        toast.error('Error loading users');
        setAvailableUsers(append ? availableUsers : []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Error loading users');
      setAvailableUsers(append ? availableUsers : []);
    } finally {
      setUserLoading(false);
    }
  };

  // User interaction functions
  const handleAddUser = (user) => {
    setSelectedUsers(prev => [...prev, user]);
    setAvailableUsers(prev => prev.filter(u => u.userID !== user.userID));
  };

  const handleRemoveUser = (user) => {
    // Remove from selected users
    setSelectedUsers(prev => prev.filter(u => u.userID !== user.userID));

    // Trigger user refresh using useEffect
    setShouldRefreshUsers(true);
  };

  const handleUserSearchChange = (field, value) => {
    const newFilters = { ...userSearchFilters, [field]: value };
    setUserSearchFilters(newFilters);

    if (userSearchTimeoutRef.current) {
      clearTimeout(userSearchTimeoutRef.current);
    }

    userSearchTimeoutRef.current = setTimeout(() => {
      setUserPage(0);
      fetchUsers(newFilters, 0, false);
    }, 500);
  };

  const handleUserScroll = () => {
    if (userListRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = userListRef.current;
      if (scrollHeight - scrollTop <= clientHeight * 1.5 && !userLoading && hasMoreUsers) {
        const nextPage = userPage + 1;
        setUserPage(nextPage);
        fetchUsers(userSearchFilters, nextPage, true);
      }
    }
  };

  // Helper to calculate deleted assignment IDs dynamically
  const getDeletedAssignmentIDs = () => {
    // Find users who were initially selected but are not in current selection
    const deletedUserIDs = initialSelectedUsers
      .filter(initialUser => !selectedUsers.some(currentUser => currentUser.userID === initialUser.userID))
      .map(user => user.userID);

    // Get assignment IDs for deleted users
    const deletedAssignmentIDs = originalAssignments
      .filter(assignment => deletedUserIDs.includes(assignment.assignedUserID))
      .map(assignment => assignment.assignmentID);

    return deletedAssignmentIDs;
  };

  // Helper to check if form changed
  const isFormChanged = () => {
    if (!initialFormRef.current) return false;
    const currentData = {
      ...formData,
      approvedCourseCode: selectedApprovedCourseCode ? selectedApprovedCourseCode.value : formData.approvedCourseCode,
      selectedUsers: selectedUsers
    };
    return JSON.stringify(currentData) !== initialFormRef.current;
  };

  // Modified handleUpdate to show modal if changed
  const handleUpdateClick = () => {
    if (!isFormChanged()) {
      toast.info('No changes made to update.');
      return;
    }
    setShowReasonModal(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };



  const handleConfirmUpdate = async () => {
    if (!selectedApprovedCourseCode || selectedUsers.length === 0) {
      toast.error('Please select an approved course code and at least one user.');
      return;
    }

    if (!reasonForChange.trim()) {
      toast.error('Please provide a reason for the change.');
      return;
    }

    try {
      // Calculate deleted assignment IDs dynamically
      const deletedAssignmentIDs = getDeletedAssignmentIDs();

      // Prepare API payload according to the specification
      const payload = {
        courseCodeID: selectedApprovedCourseCode.courseCodeID,
        assignedUserIDs: selectedUsers.map(user => user.userID),
        deletedAssignmentIDs: deletedAssignmentIDs,
        remarks: formData.remark || "",
        reasonForChange: reasonForChange.trim(),
        electronicSignature: sessionStorage.getItem('userName') || "string",
        signatureDate: new Date().toISOString() // Current date in ISO format
      };

      const result = await updateCourseCodeAssignment(payload);

      if (result?.header?.errorCount === 0) {
        const apiMessage = result.header.messages?.[0]?.messageText || 'Course code assignment updated successfully';
        toast.success(apiMessage);
        setShowReasonModal(false);
        setTimeout(() => {
          navigate('/course-code/course-code-assignment');
        }, 2000);
      } else {
        const errorMessage = result?.header?.messages?.[0]?.messageText || 'Failed to update course code assignment';
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMsg = error?.response?.data?.header?.messages?.[0]?.messageText || 'Error updating course code assignment';
      toast.error(errorMsg);
    }
  };

  const handleCancel = () => {
    navigate('/course-code/course-code-assignment');
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {showReasonModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modalContent}>
            <h3>Reason for Change</h3>
            <textarea
              value={reasonForChange}
              onChange={(e) => setReasonForChange(e.target.value)}
              rows="4"
              placeholder="Please provide a reason for this change..."
              className={styles.reasonTextarea}
            ></textarea>
            <div className={styles.modalButtons}>
              <button onClick={handleConfirmUpdate} className={styles.actionButton}>Confirm</button>
              <button onClick={() => setShowReasonModal(false)} className={styles.cancelButton}>Cancel</button>
            </div>
          </div>
        </div>
      )}
      <div className={styles.container}>
        {assignmentLoading ? (
          <div className={styles.loadingContainer}>
            <div className={styles.spinner}></div>
            <p>Loading assignment details...</p>
          </div>
        ) : (
          <form className={styles.form}>
            <div className={styles.formContent}>
              <h3 className={styles.sectionHeading}>Course Code Assignment</h3>

            <div className={styles.row}>
              <label>Approved Course Code</label>
              <Select
                options={approvedCourseCodes}
                value={selectedApprovedCourseCode}
                onChange={(selected) => {
                  setSelectedApprovedCourseCode(selected);
                  setFormData(prev => ({ ...prev, approvedCourseCode: selected ? selected.value : '' }));
                }}
                isDisabled={true}
                isClearable={false}
                placeholder="-- Select Approved Course Code --"
                className={styles.reactSelect}
              />
            </div>

            <div className={styles.row}>
              <label>Remarks</label>
              <textarea
                name="remark"
                value={formData.remark}
                onChange={handleInputChange}
                maxLength={500}
                placeholder="Enter remarks (max 500 characters)"
              />
              <small>{formData.remark.length}/500 characters</small>
            </div>

            {/* User Selection Panel */}
            <div className={styles.row}>
              <label>User Selection <span className={styles.required}>*</span></label>
              <div className={styles.userSelectionPanel}>
                {/* Left Side - User Selection Grid (70%) */}
                <div className={styles.userGridColumn}>
                  <h4>Available Users</h4>

                  {/* Search Filters */}
                  <div className={styles.searchFilters}>
                    <input
                      type="text"
                      placeholder="Search by User Name"
                      value={userSearchFilters.userName}
                      onChange={(e) => handleUserSearchChange('userName', e.target.value)}
                      className={styles.searchInput}
                    />
                    <input
                      type="text"
                      placeholder="Search by Designation"
                      value={userSearchFilters.designationName}
                      onChange={(e) => handleUserSearchChange('designationName', e.target.value)}
                      className={styles.searchInput}
                    />
                    <input
                      type="text"
                      placeholder="Search by Role"
                      value={userSearchFilters.roleName}
                      onChange={(e) => handleUserSearchChange('roleName', e.target.value)}
                      className={styles.searchInput}
                    />
                  </div>

                  {/* User Grid */}
                  <div className={styles.userGrid}>
                    <div className={styles.userGridHeader}>
                      <div>Department Name</div>
                      <div>User Name</div>
                      <div>Designation</div>
                      <div>Role Name</div>
                      <div>Action</div>
                    </div>
                    <div
                      className={styles.userGridBody}
                      ref={userListRef}
                      onScroll={handleUserScroll}
                    >
                      {availableUsers.length > 0 ? (
                        availableUsers.map(user => (
                          <div key={user.userID} className={styles.userGridRow}>
                            <div>{user.departmentName}</div>
                            <div>{user.userName}</div>
                            <div>{user.designationName}</div>
                            <div>{user.roleName}</div>
                            <div>
                              <button
                                type="button"
                                className={styles.addUserBtn}
                                onClick={() => handleAddUser(user)}
                                title="Add User"
                              >
                                +
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className={styles.noUsers}>
                          {userLoading ? 'Loading users...' : 'No users available'}
                        </div>
                      )}
                      {userLoading && (
                        <div className={styles.loadingIndicator}>Loading more users...</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Divider */}
                <div className={styles.userSelectionDivider}></div>

                {/* Right Side - Selected Users List (30%) */}
                <div className={styles.selectedUsersColumn}>
                  <h4>Selected Users ({selectedUsers.length})</h4>
                  <div className={styles.selectedUsersList}>
                    {selectedUsers.length > 0 ? (
                      selectedUsers.map(user => (
                        <div key={user.userID} className={styles.selectedUserItem}>
                          <div className={styles.userInfo}>
                            <div className={styles.userName}>{user.userName}</div>
                            <div className={styles.userDesignation}>{user.designationName}</div>
                            <div className={styles.userRole}>{user.roleName}</div>
                          </div>
                          <button
                            type="button"
                            className={styles.removeUserBtn}
                            onClick={() => handleRemoveUser(user)}
                            title="Remove User"
                          >
                            ×
                          </button>
                        </div>
                      ))
                    ) : (
                      <div className={styles.noSelectedUsers}>No users selected yet</div>
                    )}
                  </div>
                </div>
              </div>
            </div>



            <div className={styles.submitRow}>
              <button type="button" className={styles.primaryBtn} onClick={handleUpdateClick}>Update</button>
              <button type="button" onClick={handleCancel} className={styles.cancelBtn}>Cancel</button>
            </div>
          </div>
        </form>
        )}
      </div>
    </>
  );
};

export default EditCoursecodeAssignment;
