/* Common Styles for both Add and Edit Job Description */
html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
}

/* Container */
.container {
  position: absolute;
  top: 6rem;
  left: 16rem;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  box-sizing: border-box;
  height: calc(100vh - 6rem);
  width: calc(100% - 16rem);
  overflow-y: auto;
}

/* Section Heading */
.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

textarea {
  resize: vertical;
  min-height: 1em;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #ccc;
  margin-bottom: 15px;
  font-size: 16px;
  width: 100%;
  color: black;
}

/* Other common styles */
.row input,
.row select,
.row textarea {
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
  margin-bottom: 15px;
  padding-top: 14px;
  padding-bottom: 14px;
}

.labelWithPadding {
  padding-left: 8px;
  padding-top: 8px;
  display: inline-block;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

.form {
  width: 100%;
  max-width: 900px;
  background-color: #fff;
  color: #001b36;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.submitRow {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

/* Password input */
.passwordWrapper {
  position: relative;
  width: 100%;
}

.passwordWrapper input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  color: black;
  font-size: 14px;
}

.passwordWrapper input:focus {
  outline: none;
  border-color: #127c96;
  box-shadow: 0 0 0 1px #127c96;
}

.required {
  color: red;
  margin-left: 4px;
}

/* Radio Button Styles */
.radioGroup input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #001b36;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white;
}

.radioGroup input[type="radio"]:checked::before {
  content: '✔';
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 16px;
  color: #127c96;
  font-weight: bold;
  text-align: center;
  line-height: 18px;
}

.radioGroup input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(18, 124, 150, 0.3);
}

.radioGroup input[type="radio"]:checked {
  border-color: #127c96;
}

.radioGroup input[type="radio"]:checked + label {
  color: #127c96;
}

/* AddJobResposibility Specific Styles */
.add-job-form {
  background-color: #f4f7fb;
  border: 1px solid #ddd;
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.add-job-form .form input,
.add-job-form .form textarea {
  font-size: 16px;
}

.submitRow button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submitRow button[type="submit"] {
  background-color: #127C96;
  color: white;
}

.submitRow button[type="button"] {
  background-color: #ccc;
  color: white;
}

.submitRow button:hover {
  background-color: #005c6e;
}

/* Ensure proper padding on inputs and selects */
.add-job-form .form input,
.add-job-form .form select {
  padding: 12px;
  font-size: 14px;
  border-radius: 8px;
  width: 100%;
  border: 1px solid #ccc;
  margin-bottom: 15px;
}

/* Additional styling for Title, Description, and Responsibilities fields */
.titleDescriptionField {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.titleDescriptionField input,
.titleDescriptionField textarea {
  font-size: 16px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  margin-bottom: 15px;
  color: black;
}

.titleDescriptionField label {
  font-weight: 500;
  color: black;
}

/* Responsibilities section specific styles */
.responsibilitiesField {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.responsibilitiesField textarea {
  font-size: 16px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  margin-bottom: 15px;
  color: black;
}

/* React Select Custom Styles */
.reactSelect {
  width: 100%;
  font-size: 14px;
  margin-bottom: 10px;
}

/* Title Select specific styles */
.reactSelect :global(.select__control) {
  min-height: 38px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: none;
  background-color: white;
}

.reactSelect :global(.select__control:hover) {
  border-color: #40a9ff;
}

.reactSelect :global(.select__control--is-focused) {
  border-color: #40a9ff;
  box-shadow: none;
}

/* Hide dropdown indicator for title Select */
.reactSelect :global(.select__dropdown-indicator) {
  display: none;
}

.reactSelect :global(.select__menu) {
  z-index: 9999;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Title Select option styles */
.reactSelect :global(.select__option) {
  color: #000000 !important;
  cursor: pointer;
  padding: 8px 12px;
  background-color: white !important;
}

.reactSelect :global(.select__option--is-focused) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
}

.reactSelect :global(.select__option:hover) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
}

/* Title Select value styles */
.reactSelect :global(.select__single-value) {
  color: #000000 !important;
  font-weight: normal !important;
}

.reactSelect :global(.select__control--is-focused .select__single-value) {
  color: #000000 !important;
  font-weight: normal !important;
}

.reactSelect :global(.select__control:hover .select__single-value) {
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select input styles */
.reactSelect :global(.select__input) {
  color: #000000 !important;
  margin: 0;
  padding: 0;
  font-weight: normal !important;
}

.reactSelect :global(.select__input:focus) {
  outline: none;
  box-shadow: none;
}

.reactSelect :global(.select__placeholder) {
  color: #bfbfbf !important;
}

/* Title Select indicator styles */
.reactSelect :global(.select__dropdown-indicator) {
  color: #bfbfbf;
}

.reactSelect :global(.select__dropdown-indicator:hover) {
  color: #40a9ff;
}

.reactSelect :global(.select__clear-indicator) {
  color: #bfbfbf;
}

.reactSelect :global(.select__clear-indicator:hover) {
  color: #40a9ff;
}

/* Title Select container styles */
.reactSelect :global(.select__value-container) {
  padding: 0;
  color: #000000 !important;
}

.reactSelect :global(.select__value-container:focus) {
  outline: none;
  box-shadow: none;
}

/* Title Select menu styles */
.reactSelect :global(.select__menu-list) {
  padding: 0;
}

.reactSelect :global(.select__menu-list:focus) {
  outline: none;
  box-shadow: none;
}

/* Title Select input container styles */
.reactSelect :global(.select__input-container) {
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select multi-value styles */
.reactSelect :global(.select__multi-value) {
  color: #000000 !important;
  background-color: #e6f7ff !important;
}

.reactSelect :global(.select__multi-value__label) {
  color: #000000 !important;
  font-weight: normal !important;
}

.reactSelect :global(.select__multi-value__remove) {
  color: #bfbfbf !important;
}

.reactSelect :global(.select__multi-value__remove:hover) {
  color: #40a9ff !important;
}

/* Title Select focused state styles */
.reactSelect :global(.select__control--is-focused .select__single-value),
.reactSelect :global(.select__control--is-focused .select__input),
.reactSelect :global(.select__control--is-focused .select__value-container) {
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select selected option styles */
.reactSelect :global(.select__option--is-selected) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select menu list styles */
.reactSelect :global(.select__menu-list .select__option) {
  color: #000000 !important;
  font-weight: normal !important;
  background-color: white !important;
}

.reactSelect :global(.select__menu-list .select__option--is-focused),
.reactSelect :global(.select__menu-list .select__option:hover) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select menu list option styles */
.reactSelect :global(.select__menu-list .select__option),
.reactSelect :global(.select__menu-list .select__option--is-focused),
.reactSelect :global(.select__menu-list .select__option:hover),
.reactSelect :global(.select__menu-list .select__option--is-selected) {
  color: #000000 !important;
  font-weight: normal !important;
  background-color: white !important;
}

.reactSelect :global(.select__menu-list .select__option--is-focused),
.reactSelect :global(.select__menu-list .select__option:hover) {
  background-color: #e6f7ff !important;
}

/* Title Select menu list option hover styles */
.reactSelect :global(.select__menu-list .select__option:hover) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select menu list option selected styles */
.reactSelect :global(.select__menu-list .select__option--is-selected) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
  font-weight: normal !important;
}

/* Title Select menu list option focused styles */
.reactSelect :global(.select__menu-list .select__option--is-focused) {
  background-color: #e6f7ff !important;
  color: #000000 !important;
  font-weight: normal !important;
}

.readOnlyInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #666;
  cursor: not-allowed;
  font-size: 14px;
}

.readOnlyInput:focus {
  outline: none;
  border-color: #ccc;
  box-shadow: none;
} 