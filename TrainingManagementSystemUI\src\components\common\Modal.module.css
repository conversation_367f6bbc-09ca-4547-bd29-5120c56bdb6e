.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
  z-index: 1000;
  }
  
  .modal {
    background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 500px;
  max-width: 90%;
    text-align: center;
  color: #222;
}

.modal h2 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.messageContent {
  margin-bottom: 20px;
}

.messageContent p {
  margin: 0;
  color: #222;
  font-size: 1rem;
  line-height: 1.5;
  }
  
  .actions {
  display: flex;
  justify-content: center;
  gap: 15px;
    margin-top: 20px;
  }
  
  .confirmBtn {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #127C96;
    color: white;
    border: none;
  border-radius: 8px;
    cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.confirmBtn:hover {
  background-color: #0f6a83;
  }
  
  .cancelBtn {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #d0d0d0;
  color: #000000;
    border: none;
  border-radius: 8px;
    cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.cancelBtn:hover {
  background-color: #bdbdbd;
  }
  