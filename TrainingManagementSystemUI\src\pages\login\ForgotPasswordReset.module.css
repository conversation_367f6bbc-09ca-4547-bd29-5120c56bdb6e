html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

.pageWrapper {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.container {
  max-width: 400px;
  margin: 0;
  padding: 32px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.heading {
  font-size: 1.5rem;
  font-weight: 600;
  color: #127C96;
  margin-bottom: 24px;
  text-align: center;
}

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.label {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
  margin-bottom: 6px;
}

.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f9fa;
  color: #222;
  margin-bottom: 4px;
}

.input:focus {
  border-color: #127C96;
  outline: none;
  background: #fff;
}

.button {
  width: 100%;
  padding: 12px;
  background: #127C96;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 8px;
}

.button:disabled {
  background: #b2dbe4;
  cursor: not-allowed;
}

.button:hover:not(:disabled) {
  background: #0f6a83;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 0.98rem;
}

.success {
  color: #218838;
  background: #d4edda;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 0.98rem;
}

@media (max-width: 600px) {
  .container {
    padding: 16px 4px;
    max-width: 98vw;
  }
  .heading {
    font-size: 1.2rem;
  }
} 