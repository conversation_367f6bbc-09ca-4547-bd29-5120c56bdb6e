/* Common Styles for both Add and Edit Job Description */

/* Container */
.container {
  position: absolute;
  top: 6rem;
  left: 16rem;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  box-sizing: border-box;
  height: calc(100vh - 6rem);
  width: calc(100% - 16rem);
  overflow-y: auto;
}

/* Section Heading */
.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

/* Form Elements */
.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

.form {
  width: 100%;
  max-width: 900px;
  background-color: #fff;
  color: #001b36;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.submitRow {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

/* Password input */
.passwordWrapper {
  position: relative;
  width: 100%;
}

.passwordWrapper input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  color: black;
  font-size: 14px;
}

.passwordWrapper input:focus {
  outline: none;
  border-color: #127c96;
  box-shadow: 0 0 0 1px #127c96;
}

.required {
  color: red;
  margin-left: 4px;
}

/* Radio Button Styles */
.radioGroup input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #001b36;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white;
}

.radioGroup input[type="radio"]:checked::before {
  content: '✔';
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 16px;
  color: #127c96;
  font-weight: bold;
  text-align: center;
  line-height: 18px;
}

.radioGroup input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(18, 124, 150, 0.3);
}

.radioGroup input[type="radio"]:checked {
  border-color: #127c96;
}

.radioGroup input[type="radio"]:checked + label {
  color: #127c96;
}

/* EditJobResposibility Specific Styles */
.edit-job-form {
  background-color: #e8f4f9;
  border: 1px solid #ccd9e6;
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.edit-job-form .form input,
.edit-job-form .form textarea {
  font-size: 16px;
}

.edit-job-form button {
  width: 100%;
  background-color: #00376e;
}

/* Button Container Styles */
.buttonContainer {
display: flex;
justify-content: flex-end;
margin-top: 20px;
gap: 10px;
}