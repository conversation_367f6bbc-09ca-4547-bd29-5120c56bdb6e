.customSelect {
  position: relative;
  width: 100%;
}

.selectHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  cursor: pointer;
  background-color: white;
  min-height: 40px;
}

.placeholder {
  color: #666;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  margin-left: 8px;
}

.arrow.down {
  border-top: 5px solid #666;
}

.arrow.up {
  border-bottom: 5px solid #666;
}

.optionsList {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-top: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
}

.option {
  cursor: pointer;
  transition: transform 0.2s;
  flex: 0 0 auto;
}

.option:hover {
  transform: scale(1.05);
}

.option.selected {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
} 