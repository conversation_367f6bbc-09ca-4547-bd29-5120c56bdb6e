body, .container {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100%;
  }
  
  .container {
    padding: 1rem;
    height: 100vh;
    width: 100vw;
    padding-top: 5rem;
    background-color: #f9f9f9;
    box-sizing: border-box;
    overflow: hidden;
  }
  
  .departmentMaster {
    background: white;
    border-radius: 12px;
    padding: 20px;
    font-family: 'Segoe UI', sans-serif;
    color: black;
    margin-left: 16rem;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 5rem);
    overflow: hidden;
  }
  
  .panelHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .panelHeader h2 {
    font-size: 24px;
    font-weight: 600;
    color: black;
  }
  
  .controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  
  .controls input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 14px;
    width: 200px;
  }
  
  .filterBtn {
    background-color: #2c3e50;
    color: white;
    padding: 8px 14px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .addUserBtn {
    background-color: #127C96    ;
    color: white;
    padding: 8px 14px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modalContent {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    max-width: 400px;
    width: 100%;
    box-sizing: border-box;
    color: #2c3e50;
  }
  
  .modalActions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }
  
  .actions {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
  }
  
  .editBtn, .deleteBtn {
    background: transparent !important;
    border: none;
    padding: 4px;
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    width: auto;
    height: auto;
    min-width: 20px;
    min-height: 20px;
    transition: all 0.3s ease;
  }
  
  .editBtn:hover, .deleteBtn:hover {
    border-radius: 4px;
    transform: scale(1.1);
  }
  
  .actions .editIcon,
  .actions .deleteIcon {
    font-size: 18px;
    min-width: 20px;
    min-height: 15px;
    cursor: pointer;
    transition: transform 0.2s ease, color 0.2s ease;
    background: none !important;
    border-radius: 0;
  }
  
  .actions svg {
    font-size: 18px;
    cursor: pointer;
    transition: transform 0.2s ease, color 0.2s ease;
  }
  
  .actions svg:hover {
    transform: scale(1.2);
  }
  
  .actions .editIcon {
    color: #000000;
  }
  
  .actions .editIcon:active {
    transform: scale(0.95);
    color: #000000;
  }
  
  .actions .deleteIcon {
    color: #4d91a1;
  }
  
  .actions .deleteIcon:active {
    transform: scale(0.95);
    color: #65aec0;
  }
  
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #127C96;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 50px auto;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .departmentTableContainer {
    margin-top: 20px;
    flex-grow: 1;
    height: 70%;
    overflow: auto;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: #127C96 #f1f1f1;
    padding-bottom: 20px;
  }
  
  .departmentTable {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  
  .departmentTable th {
    position: sticky;
    top: 0;
    text-align: left;
    background-color: white;
    z-index: 1;
    font-weight: 600;
    font-size: 15px;
    color: black;
    padding: 15px 12px;
    border-bottom: 2px solid #dee2e6;
    font-family: 'Segoe UI', sans-serif;
  }

  .departmentTable td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    text-align: left;
    box-sizing: border-box;
    height: 60px;
    font-size: 13px;
    font-weight: 400;
  }
  
  /* Custom scrollbar styles */
  .departmentTableContainer::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .departmentTableContainer::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .departmentTableContainer::-webkit-scrollbar-thumb {
    background: #127C96;
    border-radius: 3px;
  }

  .departmentTableContainer::-webkit-scrollbar-thumb:hover {
    background: #0d5a6e;
  }

  /* Add bottom padding for the last record */
  .departmentTable tbody tr:last-child td {
    padding-bottom: 40px;
  }
  
  .pagination {
    margin-bottom: 5rem;
    display: flex;
    justify-content: flex-end;
    gap: 6px;
    flex-wrap: wrap;
  }
  
  .pagination button {
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background-color: white;
    cursor: pointer;
    color: black;
    font-size: 14px;
  }
  
  .pagination .active {
    background-color: #127C96    ;
    color: white;
    font-weight: bold;
  }

  .userTableContainer {
    margin-top: 20px;
    flex-grow: 1;
    height: 70%;
    overflow: auto;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: #127C96 #f1f1f1;
    padding-bottom: 20px;
  }

  /* Custom scrollbar styles */
  .userTableContainer::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .userTableContainer::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .userTableContainer::-webkit-scrollbar-thumb {
    background: #127C96;
    border-radius: 3px;
  }

  .userTableContainer::-webkit-scrollbar-thumb:hover {
    background: #0d5a6e;
  }

  /* Add bottom padding for the last record */
  .userTable tbody tr:last-child td {
    padding-bottom: 40px;
  }

  .userTable th {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
  }