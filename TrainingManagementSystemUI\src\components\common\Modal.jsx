import React from 'react';
import styles from './Modal.module.css';

const Modal = ({ title, message, onConfirm, onCancel }) => {
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <h2>{title}</h2>
        <div className={styles.messageContent}>
          {typeof message === 'string' ? <p>{message}</p> : message}
        </div>
        <div className={styles.actions}>
          <button className={styles.confirmBtn} onClick={onConfirm}>Confirm</button>
          <button className={styles.cancelBtn} onClick={onCancel}>Cancel</button>
        </div>
      </div>
    </div>
  );
};

export default Modal;