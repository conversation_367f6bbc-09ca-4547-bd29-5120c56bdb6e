.container {
  padding: 1rem;
  height: 100vh;
  width: 100vw;
  padding-top: 5rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.documentReview {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  margin-left: 16rem;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 5rem);
  overflow: hidden;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.searchInput {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
}

.docTableContainer {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  position: relative;
  margin-bottom: 20px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.docTableContainer::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.docTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  border-spacing: 0;
}


.docTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 12px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
}

.docTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
}

/* Column widths and alignments */
.docTable th:nth-child(1), /* Document Name */
.docTable td:nth-child(1) {
  width: 150px;
  min-width: 200px;
  text-align: left;
}

.docTable th:nth-child(2), /* Document Code */
.docTable td:nth-child(2) {
  width: 150px;
  min-width: 150px;
  text-align: left;
}

.docTable th:nth-child(3), /* Document Type */
.docTable td:nth-child(3) {
  width: 120px;
  min-width: 150px;
  text-align: left;
}

.docTable th:nth-child(4), /* Version */
.docTable td:nth-child(4) {
  width: 60px;
  min-width: 80px;
  text-align: center;
}

.docTable th:nth-child(5), /* Activity */
.docTable td:nth-child(5) {
  width: 150px;
  min-width: 150px;
  text-align: center;
}

.docTable th:nth-child(6), /* Actions */
.docTable td:nth-child(6) {
  width: 200px;
  min-width: 200px;
  text-align: center;
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0;
  width: 100%;
}

.actionGroup {
  display: flex;
  gap: 15px;
}

.actionGroupSmall {
  display: flex;
  gap: 2px;
}

.activity {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
}

.editBtn, .deactivateBtn, .downloadBtn, .viewBtn, .actionButton {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
}
.editBtn:focus, .deactivateBtn:focus, .downloadBtn:focus, .viewBtn:focus, .actionButton:focus,
.editBtn:active, .deactivateBtn:active, .downloadBtn:active, .viewBtn:active, .actionButton:active,
.editBtn:hover, .deactivateBtn:hover, .downloadBtn:hover, .viewBtn:hover, .actionButton:hover {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  background: transparent;
  border: none;
  padding: 2px;
  cursor: pointer;
  min-width: 60px;
}

.actionButton span {
  font-size: 10px;
  color: #666;
  white-space: nowrap;
}

.viewIcon,
.downloadIcon {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.viewIcon:hover,
.downloadIcon:hover {
  transform: scale(1.1);
}

.viewIcon:active,
.downloadIcon:active {
  transform: scale(0.95);
}

.viewIcon {
  color: #2795b1;
}

.downloadIcon {
  color: #127C96;
}

.approveIcon {
  color: #2e7d32;
}

.returnIcon {
  color: #f57c00;
}

.rejectIcon {
  color: #c62828;
}

.actions svg {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.actions svg:hover {
  transform: scale(1.1);
}

.actions svg:active {
  transform: scale(0.95);
}

.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Status badge styles */
.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.statusDraft {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.statusApproved {
  background-color: #e6f4ea;
  color: #1e7e34;
  border: 1px solid #c3e6cb;
}

.statusReturn {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.statusReject {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Responsive styles */
@media (max-width: 768px) {
  .documentReview {
    margin-left: 0;
    margin-right: 0;
  }

  .actions svg {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .documentReview {
    padding: 15px;
  }

  .actions svg {
    font-size: 24px;
  }
}

/* PDF Viewer styles */
.FileViewerOverlayOuter {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.FileViewerCenteredContainer {
  position: relative;
  width: 70vw;
  height: 80vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.FileViewerInnerContainer {
  flex-grow: 1;
  height: 100%;
  width: 100%;
}

.FileViewerCloseBtnWrapper {
  text-align: right;
}

.FileViewerCloseBtn {
  cursor: pointer;
  background-color: transparent;
  border: none;
  font-weight: 600;
  font-size: 16px;
  padding: 5px 10px;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.FileViewerCloseBtn:hover {
  color: #555;
}

/* Pagination container styles */
.paginationContainer {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 10px 0;
  background-color: white;
  margin-top: auto;
}

.rejectModalContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rejectionRemarks {
  width: 100%;
  min-height: 100px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.rejectionRemarks:focus {
  outline: none;
  border-color: #127C96;
  box-shadow: 0 0 0 2px rgba(18, 124, 150, 0.1);
}


.confirmButton {
  background-color: #2e7d32; /* green */
  color: #fff;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
}

.cancelButton {
  background-color: #c62828; /* red */
  color: #fff;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.actionDivider {
  display: inline-block;
  width: 1px;
  height: 24px;
  background: #e0e0e0;
  margin: 0 5px;
  vertical-align: middle;
}

.reasonInput {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reasonInput label {
  font-weight: 500;
  color: #333;
}

.reasonInput textarea {
  width: 100%;
  min-height: 100px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.reasonInput textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Remove old rejection remarks styles */
.rejectModalContent,
.rejectionRemarks {
  display: none;
}