.container {
    position: absolute;
    top: 6rem;
    left: 16rem;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    box-sizing: border-box;
    height: calc(100vh - 6rem);
    width: calc(100% - 16rem);
    overflow-y: auto;
  }
  
  .sectionHeading {
    font-size: 22px;
    font-weight: 600;
    color: #00376e;
    margin-top: 10px;
    margin-bottom: 30px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
  }
  
  .form {
    width: 100%;
    max-width: 900px;
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .row {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
  }
  
  .row label {
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: black;
  }
  
  .row input,
  .row select {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    width: 100%;
    color: black;
  }
  
  .submitRow {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  .submitRow button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .submitRow button[type="submit"] {
    background-color: #127C96    ;
    color: white;
  }
  
  .submitRow button:hover[type="submit"] {
    background-color: #187188;
  }
  
  .submitRow button[type="button"] {
    background-color: #a3a3a3;
    color: white;
  }

  .submitRow button:hover {
    background-color: #999898;
  }

  

  
  
  @media (max-width: 768px) {
    .form {
      padding: 20px;
      max-width: 95%;
    }
  }
  
  @media (max-width: 480px) {
    .form {
      padding: 15px;
    }
  }
  .required {
    color: red;
    margin-left: 2px;
  }
  .error {
    color: red;
    background-color: #ffe6e6;
    padding: 10px;
    margin-bottom: 10px;
  }
  
  .success {
    color: green;
    background-color: #e0f7e9;
    padding: 10px;
    margin-bottom: 10px;
  }
  
  .loadingBar {
    height: 4px;
    background: linear-gradient(to right, #4caf50, #8bc34a);
    animation: loading 1.5s linear infinite;
  }
  
  @keyframes loading {
    0% { width: 0%; }
    100% { width: 100%; }
  }
  
  .noData {
    text-align: center;
    padding: 15px;
    color: #666;
  }  