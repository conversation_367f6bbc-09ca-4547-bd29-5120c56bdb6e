.chip {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.completed {
  background-color: #e6f4ea;
  color: #1e7e34;
  border: 1px solid #c3e6cb;
}

.overdue {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.inProgress {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.assigned {
  background-color: #e6f4ea;
  color: #1e7e34;
  border: 1px solid #c3e6cb;
}

.default {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
} 