import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEdit, <PERSON>a<PERSON>ye, <PERSON>aB<PERSON> } from 'react-icons/fa';
import Pagination from '../../../components/pagination/Pagination';
import styles from './coursecode.module.css';
import { fetchCourseCodesByUserId, getCourseCodeById, deleteCourseCode } from '../../../services/course-code/CoursecodeService';
import Modal from '../../../components/common/Modal';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CoursecodeView from '../../../components/common/coursecode/CoursecodeView';

const Coursecode = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [coursecodeData, setCoursecodeData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCoursecode, setSelectedCoursecode] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewCourseCodeDetails, setViewCourseCodeDetails] = useState(null);
  const itemsPerPage = 10;

  const tableContainerRef = useRef(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 2000);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm,
          showOnlyUnderReview: false
        };
        const res = await fetchCourseCodesByUserId(payload);
        setCoursecodeData(res.coursecodes || []);
        setTotalRecords(res.totalRecord || 0);
        console.log(res);
      } catch {
        setCoursecodeData([]);
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page on search
  };

  const handleAddCoursecode = () => {
    navigate('/course-code/course-code-registration/add');
  };

  const handleEdit = (id) => {
    navigate(`/course-code/course-code-registration/edit/${id}`);
  };

  const handleView = async (coursecode) => {
    try {
      const res = await getCourseCodeById(coursecode.courseCodeID);
      setViewCourseCodeDetails(res.coursecode);
      setShowViewModal(true);
    } catch {
      toast.error('Failed to fetch course code details');
    }
  };

  const handleCloseView = () => {
    setShowViewModal(false);
    setViewCourseCodeDetails(null);
  };

  const handleDeactivate = (coursecode) => {
    setSelectedCoursecode(coursecode);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedCoursecode) return;
    setLoading(true);
    try {
      const modifiedBy = sessionStorage.getItem('userName') || 'system';
      const res = await deleteCourseCode(selectedCoursecode.courseCodeID, modifiedBy);
      if (res?.header?.errorCount === 0) {
        toast.success(res.header.messages?.[0]?.messageText || 'Course code deactivated successfully.');
        // Refresh data
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: searchTerm,
          showOnlyUnderReview: false
        };
        const fetchRes = await fetchCourseCodesByUserId(payload);
        setCoursecodeData(fetchRes.coursecodes || []);
        setTotalRecords(fetchRes.totalRecord || 0);
      } else {
        toast.error(res?.header?.messages?.[0]?.messageText || 'Failed to deactivate course code.');
      }
    } catch (error) {
      console.error('Error deactivating course code:', error);
      toast.error('An error occurred while deactivating course code.');
    } finally {
      setLoading(false);
      setShowDeleteModal(false);
      setSelectedCoursecode(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedCoursecode(null);
  };

  const getStatusClass = (status) => {
    switch ((status || '').toLowerCase()) {
      case 'active':
      case 'approved':
        return styles.statusApproved;
      case 'inactive':
      case 'rejected':
        return styles.statusReject;
      case 'returned':
        return styles.statusReturn;
      case 'underreview':
        return styles.statusUnderReview;
      default:
        return '';
    }
  };

  const handleTooltipPosition = (event) => {
    const tooltip = event.currentTarget.querySelector(`.${styles.tooltipText}`);
    if (tooltip) {
      const rect = event.currentTarget.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      tooltip.style.top = `${rect.top + scrollTop - tooltip.offsetHeight - 10}px`;
      tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <div className={styles.container}>
        <div className={styles.documentMaster}>
          <div className={styles.panelHeader}>
            <h2>Course Code Registration</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={handleSearch}
                className={styles.searchInput}
              />
              <button onClick={handleAddCoursecode} className={styles.addDocBtn}>
                + Add
              </button>
            </div>
          </div>

          <div className={styles.docTableContainer} ref={tableContainerRef}>
            <table className={styles.docTable}>
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Course Code</th>
                  <th>Department</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="5" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : coursecodeData.length === 0 ? (
                  <tr>
                    <td colSpan="5" style={{ textAlign: 'center', padding: '20px' }}>
                      No Course Code records found.
                    </td>
                  </tr>
                ) : (
                  coursecodeData.map((item) => (
                    <tr key={item.courseCodeID}>
                      <td>{item.courseTitle}</td>
                      <td>{item.courseCode}</td>
                      <td>{item.departmentName}</td>
                      <td>
                        <div
                          className={styles.tooltipWrapper}
                          onMouseEnter={handleTooltipPosition}
                        >
                          <span
                            className={`${styles.statusBadge} ${getStatusClass(item.courseCodeStatus)}`}
                          >
                            {item.courseCodeStatus}
                          </span>
                          <span className={styles.tooltipText}>{item.approvalRemarks || 'No remarks available'}</span>
                        </div>
                      </td>
                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleView(item)}
                            title="View"
                          >
                            <FaEye className={styles.viewIcon} />
                            <span>View</span>
                          </button>
                          {/* Divider */}
                          <div className={styles.actionDivider}></div>
                          {/* Edit Button */}
                          <button
                            className={styles.actionButton}
                            onClick={() => handleEdit(item.courseCodeID)}
                            title="Edit"
                            disabled={item.courseCodeStatus === 'Approved' || item.courseCodeStatus === 'Rejected'}
                          >
                            <FaEdit className={styles.editIcon} />
                            <span>Edit</span>
                          </button>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleDeactivate(item)}
                            title="Deactivate"
                            disabled={item.status === 'Approved'}
                          >
                            <FaBan className={styles.deactivateIcon} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          <div className={styles.paginationContainer}>
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalRecords / itemsPerPage)}
              paginate={handlePageChange}
            />
          </div>
        </div>
      </div>
      {showDeleteModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate Course Code "${selectedCoursecode?.courseTitle}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
      {showViewModal && (
        <CoursecodeView 
          courseCodeDetails={viewCourseCodeDetails}
          onClose={handleCloseView}
        />
      )}
    </>
  );
};

export default Coursecode;
