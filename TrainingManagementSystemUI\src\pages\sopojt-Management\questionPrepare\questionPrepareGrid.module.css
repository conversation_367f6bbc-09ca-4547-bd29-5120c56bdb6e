body, .container {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
}

.container {
  padding: 1rem;
  height: 100vh;
  width: 100vw;
  padding-top: 5rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: auto;
}

.questionMaster {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  margin-left: 16rem;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 5rem);
  overflow: hidden;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
  margin: 0;
  padding: 0;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
  margin-left: auto;
  flex-shrink: 0;
}

.searchContainer {
  position: relative;
  display: flex;
  align-items: center;
  width: 250px;
}

.searchInput {
  padding: 8px 12px 8px 35px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 100%;
  background-color: white;
  color: black;
  box-sizing: border-box;
  height: 34px;
  transition: border-color 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #137688;
  box-shadow: 0 0 0 2px rgba(19, 118, 136, 0.1);
}

.searchInput::placeholder {
  color: #a9a9a9;
}

.searchIcon {
  position: absolute;
  left: 12px;
  color: #666;
  font-size: 14px;
  z-index: 1;
  pointer-events: none;
}

.clearIcon {
  position: absolute;
  right: 12px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  z-index: 1;
  transition: color 0.3s ease;
}

.clearIcon:hover {
  color: #137688;
}

.searchSpinner {
  position: absolute;
  right: 35px;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(19, 118, 136, 0.2);
  border-left-color: #137688;
  border-radius: 50%;
  animation: searchSpin 1s linear infinite;
}

@keyframes searchSpin {
  to {
    transform: rotate(360deg);
  }
}

.controls input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
  background-color: white;
  color: black;
  box-sizing: border-box;
  height: 34px;
}

.controls input[type="text"]::placeholder {
  color: #a9a9a9;
}

.filterBtn, .addUserBtn {
  background-color: #137688;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.3s ease;
  box-sizing: border-box;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filterBtn:hover, .addUserBtn:hover {
  background-color: #0f6a83;
}

.userTableContainer {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  position: relative;
  margin-bottom: 20px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  height: calc(100vh - 200px);
  min-height: 400px;
}

.userTableContainer::-webkit-scrollbar {
  display: none;
}

.userTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.userTable th,
.userTable td {
  min-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.userTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 12px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
}

.userTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
}

/* Document Name */
.userTable th:nth-child(1),
.userTable td:nth-child(1) {
  width: 200px;
}

/* Document Code */
.userTable th:nth-child(2),
.userTable td:nth-child(2) {
  width: 120px;
}

/* Total Questions */
.userTable th:nth-child(3),
.userTable td:nth-child(3) {
  width: 120px;
}

/* Status */
.userTable th:nth-child(4),
.userTable td:nth-child(4) {
  width: 220px;
}

/* Actions */
.userTable th:nth-child(5),
.userTable td:nth-child(5) {
  width: 180px;
  text-align: center;
}

/* Style for the Actions column header and cells */
.userTable th:nth-child(5) {
  text-align: center;
  vertical-align: middle;
}

.userTable td:nth-child(5) {
  text-align: center;
  padding: 0 8px;
  vertical-align: middle;
}

/* Action buttons */
.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  width: 100%;
}

.editBtn, .deactivateBtn, .downloadBtn, .viewBtn {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 4px 8px !important;
  margin: 0;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
  gap: 4px;
}

.editBtn:focus, .deactivateBtn:focus, .downloadBtn:focus, .viewBtn:focus,
.editBtn:active, .deactivateBtn:active, .downloadBtn:active, .viewBtn:active,
.editBtn:hover, .deactivateBtn:hover, .downloadBtn:hover, .viewBtn:hover {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.editBtn span, .deactivateBtn span, .downloadBtn span, .viewBtn span {
  font-size: 10px;
  color: #4d4b4b;
  white-space: nowrap;
}

.editBtn:disabled, .deactivateBtn:disabled, .downloadBtn:disabled, .viewBtn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.editBtn:not(:disabled):hover .editIcon,
.deactivateBtn:not(:disabled):hover .deleteIcon,
.downloadBtn:not(:disabled):hover .downloadIcon,
.viewBtn:not(:disabled):hover .viewIcon {
  transform: scale(1.1);
}

.editIcon, .deleteIcon, .downloadIcon, .viewIcon {
  font-size: 16px;
  min-width: 20px;
  min-height: 15px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewIcon {
  color: #0090b4;
}

.downloadBtn {
  margin-right: 25px;
}

.editIcon {
  color: #000000;
}

.deleteIcon {
  color: #dc3545;
}

.downloadIcon {
  color: #127C96;
}

.actionDivider {
  display: inline-block;
  width: 1px;
  height: 24px;
  background: #e0e0e0;
  margin: 0 10px;
  vertical-align: middle;
}

/* Column widths with minimum sizes */
.userTable th:nth-child(1), .userTable td:nth-child(1) { /* Document Name */
  min-width: 200px;
}
.userTable th:nth-child(2), .userTable td:nth-child(2) { /* Code */
  min-width: 150px;
}
.userTable th:nth-child(3), .userTable td:nth-child(3) { /* Total Questions */
  min-width: 100px;
  text-align: center;
}
.userTable th:nth-child(4), .userTable td:nth-child(4) { /* Status */
  width: 100px;
  min-width: 100px;
  text-align: center;
}
.userTable th:nth-child(5), .userTable td:nth-child(5) { /* Action */
  min-width: 200px;
  text-align: center;
}

.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.paginationContainer {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 10px 0;
  background-color: white;
  margin-top: auto;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.statusDraft {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.statusApproved {
  background-color: #e6f4ea;
  color: #1e7e34;
  border: 1px solid #c3e6cb;
}

.statusReturn {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.statusReject {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
  .questionMaster {
    margin-left: 0;
    margin-right: 0;
  }

  .actions svg {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .questionMaster {
    padding: 15px;
  }

  .actions svg {
    font-size: 24px;
  }
}

/* Tooltip styles */


/* Tooltip styles */
.tooltipWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.tooltipText {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: fixed;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: normal;
  word-wrap: break-word;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  pointer-events: none;
}

.tooltipText::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -12px;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #333;
}

.tooltipWrapper:hover .tooltipText {
  visibility: visible;
  opacity: 1;
}


.questionApproveMaster {
  overflow: hidden;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #f5c6cb;
  font-size: 14px;
}

.noResults {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

.searchResults {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  color: #0066cc;
  font-size: 14px;
}