import React, { useState, useEffect, useContext, useRef } from 'react';
import Modal from '../../../../components/common/Modal';
import { fetchDocumentTypes } from '../../../../services/lookup/lookupService';
import styles from '../register/RegisterDocument.module.css';
import Navbar from '../../../../components/Navbar/Navbar';
import Sidebar from '../../../../components/Sidebar/Sidebar';
import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { DocumentContext } from '../../../../context/sopOjt-Management/DocumentContext';
import { updateDocument } from '../../../../services/sopojt-Management/DocumentRegistrationService'
// import updateDocument from '../../../../services/sopojt-Management/DocumentRegistrationService';

const EditDocument = () => {
  const navigate = useNavigate();
  const { documentDetails } = useContext(DocumentContext);

  const [documentTypes, setDocumentTypes] = useState([]);
  const [selectedDocType, setSelectedDocType] = useState(null);
  const [formData, setFormData] = useState({
    id: '',
    documentName: '',
    uniqueCode: '',
    documentType: '',
    documentVersion: '',
    effectiveFrom: '',
    nextReviewDate: '',
    uploadFile: null,
    remarks: '',
    estimatedReadingTime: '',
    createdDate: new Date().toISOString().split('T')[0],
    documentStatus: '',
    reasonForChange: '',
  });
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');
  const initialFormRef = useRef(null);

// Load document types once on mount
// 1. Load document types once on mount
useEffect(() => {
  const loadDocumentTypes = async () => {
    try {
      const data = await fetchDocumentTypes();
      if (data?.documentTypes) {
        const options = data.documentTypes.map(type => ({
          value: type.documentTypeID,
          label: type.documentTypeName,
        }));
        setDocumentTypes(options);
      }
      // Show API message after setting document types
      if (data?.header?.errorCount === 0) {
        // Success, do nothing
      } else if (data?.header?.errorCount > 0) {
        const errMsg = data.header.messages?.[0]?.messageText;
        if (errMsg) toast.error(errMsg);
      }
    } catch {
      toast.error('Failed to load document types');
    }
  };
  loadDocumentTypes();
}, []);

// 2. Load documentDetails/localStorage and set basic form data (without documentType for now)
useEffect(() => {
  if (documentDetails) {
    setFormData({
      id: documentDetails.documentID || '',
      documentName: documentDetails.documentName || '',
      uniqueCode: documentDetails.documentCode || '',
      documentVersion: documentDetails.documentVersion || '',
      effectiveFrom: documentDetails.effectiveFrom || '',
      nextReviewDate: documentDetails.nextReviewDate || '',
      uploadFile: null,
      remarks: documentDetails.remarks || '',
      estimatedReadingTime: documentDetails.estimatedReadingTime || '',
      createdDate: documentDetails.createdDate || new Date().toISOString().split('T')[0],
      documentStatus: documentDetails.documentStatus || '',
      reasonForChange: '', // if you expect it from documentDetails, use documentDetails.reasonForChange || ''
    });
    // handleDownloadDocumentFile(documentDetails.documentID);
  } else {
    const storedDoc = localStorage.getItem('editDocumentData');
    if (storedDoc) {
      const docData = JSON.parse(storedDoc);
      setFormData({
        id: docData.documentID || '',
        documentName: docData.documentName || '',
        uniqueCode: docData.documentCode || '',
        documentVersion: docData.documentVersion || '',
        effectiveFrom: docData.effectiveFrom || '',
        nextReviewDate: docData.nextReviewDate || '',
        uploadFile: null,
        remarks: docData.remarks || '',
        estimatedReadingTime: docData.estimatedReadingTime || '',
        createdDate: docData.createdDate || new Date().toISOString().split('T')[0],
        documentStatus: docData.documentStatus || '',
        reasonForChange: docData.reasonForChange || '',
      });
      // handleDownloadDocumentFile(docData.documentID);
    } else {
      toast.error('No document selected for editing');
      navigate('/document-management/document-registration');
    }
  }
}, [documentDetails, navigate]);

// 3. Once documentTypes and formData (without documentType) are ready, match and set documentType ID and selectedDocType
useEffect(() => {
  // The document type name from the form data (from doc details or localStorage)
  const docTypeName = documentDetails?.documentType || (() => {
    const storedDoc = localStorage.getItem('editDocumentData');
    if (storedDoc) {
      const docData = JSON.parse(storedDoc);
      return docData.documentType || '';
    }
    return '';
  })();

  if (documentTypes.length > 0 && docTypeName) {
    const matchedOption = documentTypes.find(opt => opt.label === docTypeName);
    if (matchedOption) {
      setSelectedDocType(matchedOption);
      setFormData(prev => ({ ...prev, documentType: matchedOption.value }));
    }
  }
}, [documentTypes, documentDetails]);

// Store initial form data for change detection
useEffect(() => {
  if (formData && documentTypes.length > 0) {
    // Only store after all initial data is loaded
    initialFormRef.current = JSON.stringify({ ...formData, documentType: formData.documentType });
  }
  // eslint-disable-next-line
}, [documentTypes, formData.id]);

// Helper to check if form changed
const isFormChanged = () => {
  if (!initialFormRef.current) return false;
  return JSON.stringify({ ...formData, documentType: formData.documentType }) !== initialFormRef.current;
};

// Modified handleUpdate to show modal if changed
const handleUpdateClick = () => {
  if (!isFormChanged()) {
    toast.info('No changes made to update.');
    return;
  }
  setShowReasonModal(true);
};

// Confirm update with reason
const handleConfirmUpdate = async () => {
  if (!reasonForChange.trim()) {
    toast.error('Reason for change is required.');
    return;
  }
  setFormData(prev => ({ ...prev, reasonForChange }));
  setShowReasonModal(false);
  await handleSubmit(reasonForChange);
};

  const handleChange = (e) => {
    const { name, value, type, files } = e.target;
    
    // Special handling for Effective From date to update Next Review Date min
    if (name === 'effectiveFrom') {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        // If Next Review Date is before new Effective From date, reset it
        nextReviewDate: prev.nextReviewDate && new Date(prev.nextReviewDate) < new Date(value) 
          ? prev.nextReviewDate 
          : ''
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === 'file' ? files[0] : value,
      }));
    }
  };

  const validateDocumentName = (name) => {
    if (name.length < 5) {
      return 'Document name must be at least 5 characters long';
    }
    if (name.length > 100) {
      return 'Document name must not exceed 100 characters';
    }
    if (!/^[a-zA-Z0-9\s\-_.,()]+$/.test(name)) {
      return 'Document name can only contain letters, numbers, spaces, and basic punctuation';
    }
    return null;
  };

  const validateDates = (effectiveFrom, nextReviewDate) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const effectiveDate = new Date(effectiveFrom);
    const reviewDate = new Date(nextReviewDate);

    if (effectiveDate < today) {
      return 'Effective From date cannot be in the past';
    }

    if (reviewDate <= effectiveDate) {
      return 'Next Review Date must be after Effective From Date';
    }

    // Maximum review period (e.g., 2 years)
    const maxReviewPeriod = new Date(effectiveDate);
    maxReviewPeriod.setFullYear(maxReviewPeriod.getFullYear() + 2);
    if (reviewDate > maxReviewPeriod) {
      return 'Next Review Date cannot be more than 2 years after Effective From Date';
    }

    return null;
  };

  const validateFile = (file) => {
    if (file) {
      // File type validation
      const allowedTypes = [
        'application/pdf',
        'application/vnd.ms-powerpoint', // .ppt
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
        'video/mp4'
      ];
      if (!allowedTypes.includes(file.type)) {
        return 'Only PDF, PPT, PPTX, and MP4 files are allowed';
      }

      // File size validation (500MB)
      const maxFileSize = 500 * 1024 * 1024;
      if (file.size > maxFileSize) {
        return 'File size must be less than 500MB';
      }
    }
    return null;
  };

  const handleSubmit = async (reasonOverride) => {
    const nameError = validateDocumentName(formData.documentName);
    if (nameError) {
      toast.error(nameError);
      return;
    }

    if (!formData.documentType) {
      toast.error('Please select a document type');
      return;
    }

    // const versionRegex = /^\d+\.\d+$/;
    // if (!versionRegex.test(formData.documentVersion)) {
    //   toast.error('Version must be in the format X.Y (e.g., 1.0, 2.1)');
    //   return;
    // }

    if (!formData.estimatedReadingTime || isNaN(formData.estimatedReadingTime) || Number(formData.estimatedReadingTime) <= 0) {
      toast.error('Estimated Reading Time must be a positive number');
      return;
    }

    const dateError = validateDates(formData.effectiveFrom, formData.nextReviewDate);
    if (dateError) {
      toast.error(dateError);
      return;
    }

    const fileError = validateFile(formData.uploadFile);
    if (fileError) {
      toast.error(fileError);
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append('DocumentID', formData.id);  // Include document ID for update
    formDataToSend.append('DocumentPath', '');
    if (formData.uploadFile) {
      formDataToSend.append('DocumentExtention', `.${formData.uploadFile.name.split('.').pop()}`);
      formDataToSend.append('file', formData.uploadFile);
    } else {
      formDataToSend.append('DocumentExtention', '');
    }
    formDataToSend.append('SignatureDate', new Date().toISOString());
    formDataToSend.append('PlantID', 0);
    formDataToSend.append('DocumentName', formData.documentName);
    formDataToSend.append('DocumentCode', formData.uniqueCode);
    formDataToSend.append('DocumentTypeID', formData.documentType);
    formDataToSend.append('Remarks', formData.remarks || '');
    formDataToSend.append('ReasonForChange', reasonOverride || formData.reasonForChange);
    formDataToSend.append('ElectronicSignature', 'string');
    formDataToSend.append('EstimatedReadingTime', formData.estimatedReadingTime);
    formDataToSend.append('DocumentVersion', formData.documentVersion);
    formDataToSend.append('CreatedBy', formData.createdBy || 'string');
    formDataToSend.append('DocumentStatus', formData.documentStatus);

    try {
      const result = await updateDocument(formDataToSend);
      console.log(result)

      if (result?.header?.errorCount === 0) {
        const apiMessage = result.header.messages?.[0]?.messageText || 'Document updated successfully';
        toast.success(apiMessage);
        setTimeout(() => {
          navigate('/document-management/document-registration');
        }, 2000);
      } else {
        const errorMessage = result?.header?.messages?.[0]?.messageText || 'Failed to update document';
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMsg = error?.response?.data?.header?.messages?.[0]?.messageText || 'Error updating document';
      toast.error(errorMsg);
      console.error(error);
    }
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the document "{formData.documentName}" ({formData.uniqueCode})</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  id="reasonForChange"
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={() => setShowReasonModal(false)}
        />
      )}
      <div className={styles.container}>
        <form className={styles.form}>
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Document Registration</h3>

            <div className={styles.row}>
              <label>Document Name <span className={styles.required}>*</span></label>
              <input 
                type="text" 
                name="documentName" 
                value={formData.documentName} 
                onChange={handleChange} 
                required 
                placeholder="Enter document name (5-100 characters)"
              />
            </div>

            <div className={styles.row}>
              <label>Unique Code <span className={styles.required}>*</span></label>
              <input 
                type="text" 
                name="uniqueCode" 
                value={formData.uniqueCode} 
                onChange={handleChange} 
                required 
                placeholder="e.g., SOP-001"
                disabled // Unique code should not be editable
              />
            </div>

            <div className={styles.row}>
              <label>Document Type <span className={styles.required}>*</span></label>
              <Select
                options={documentTypes}
                value={selectedDocType}
                onChange={(selected) => {
                  setSelectedDocType(selected);
                  setFormData(prev => ({ ...prev, documentType: selected ? selected.value : '' }));
                }}
                isClearable
                placeholder="-- Select Document Type --"
                className={styles.reactSelect}
              />
            </div>

            <div className={styles.row}>
              <label>Version <span className={styles.required}>*</span></label>
              <input 
                type="text" 
                name="documentVersion" 
                value={formData.documentVersion} 
                onChange={handleChange} 
                required 
                placeholder="e.g., 1.0"
              />
            </div>

            <div className={styles.row}>
              <label>Estimated Reading Time (minutes) <span className={styles.required}>*</span></label>
              <input 
                type="number"
                min="1"
                name="estimatedReadingTime"
                value={formData.estimatedReadingTime}
                onChange={handleChange}
                required
                placeholder="Enter estimated reading time"
                onWheel={(e) => e.target.blur()}
                onKeyDown={(e) => {
                  if (!['Backspace', 'ArrowLeft', 'ArrowRight', 'Delete', 'Tab'].includes(e.key) && !/^\d$/.test(e.key)) {
                    e.preventDefault();
                  }
                }}
              />
            </div>

            {/* Effective From and Next Review Date fields removed */}

          <div className={styles.row}>
            <label>Document File</label>
            <input 
              type="file" 
              name="uploadFile" 
              onChange={handleChange} 
              accept=".pdf,.ppt,.pptx,.mp4"
            />
            <small>Accepted formats: PDF, PPT, PPTX, MP4 (Max size: 500MB)</small>
            {/* Show current file name as: Document Name + extension (from API/localStorage) if no new upload, styled like EditOJTMaster */}
            {!formData.uploadFile && (documentDetails?.documentExtention || (() => {
              const storedDoc = localStorage.getItem('editDocumentData');
              if (storedDoc) {
                const docData = JSON.parse(storedDoc);
                return docData.documentExtention;
              }
              return '';
              
            })()) && (
              <div className={styles.fileDetails}>
                <p>
                  Current File: {formData.documentName}
                  {(() => {
                    // Prefer API ext, fallback to localStorage ext
                    let ext = documentDetails?.documentExtention;
                    if (!ext) {
                      const storedDoc = localStorage.getItem('editDocumentData');
                      if (storedDoc) {
                        const docData = JSON.parse(storedDoc);
                        ext = docData.documentExtention;
                      }
                    }
                    if (ext) {
                      return ext.startsWith('.') ? ext : '.' + ext;
                    }
                    return '';
                  })()}
                </p>
              </div>
            )}
            
          </div>


            <div className={styles.row}>
              <label>Remarks</label>
              <textarea 
                name="remarks" 
                value={formData.remarks} 
                onChange={handleChange} 
                maxLength={500} 
                placeholder="Enter remarks (max 500 characters)"
              />
              <small>{formData.remarks.length}/500 characters</small>
            </div>

            <div className={styles.submitRow}>
              <button type="button" className={styles.primaryBtn} onClick={handleUpdateClick}>Update</button>
              <button type="button" onClick={() => navigate(-1)} className={styles.cancelBtn}>Cancel</button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default EditDocument;
