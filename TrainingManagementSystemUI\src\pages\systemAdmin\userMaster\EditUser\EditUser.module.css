.container {
  position: absolute;
  top: 6rem;
  left: 16rem;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Align to top instead of center */
  padding: 20px;
  box-sizing: border-box;
  height: calc(100vh - 6rem);
  width: calc(100% - 16rem);
  overflow-y: auto; /* Allows vertical scroll if needed */
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.dateInputWrapper {
  position: relative;
}

.dateInput {
  width: 100%;
  padding-right: 30px;
}

.calendarIcon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
  font-size: 18px;
  pointer-events: none;
}


.radioGroup {
  display: flex;
  align-items: center;
  gap: 1rem;              /* Space between Yes and No */
  margin-top: 0.5rem;
  width: fit-content;     /* Only take space needed */
}

.radioGroup label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: black;
  cursor: pointer;
  margin-right: 1rem; /* space between Yes and No groups */
}

.radioGroup label input {
  margin-right: 4px; /* tighter space between radio button and text */
}


/* Custom circular radio buttons */
.radioGroup input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #001b36;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white; /* Ensure the background is white */
}

/* Hide the default dot and show the checkmark */
.radioGroup input[type="radio"]:checked::before {
  content: '✔'; /* Unicode checkmark */
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 16px;
  color: #127C96; /* Primary color */
  font-weight: bold;
  text-align: center;
  line-height: 18px;
}

/* Custom focus outline */
.radioGroup input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(18, 124, 150, 0.3); /* Custom focus color */
}

/* Style for the radio buttons on hover */
.radioGroup input[type="radio"]:hover {
  border-color: #127C96; /* Change border color on hover */
}

.radioGroup input[type="radio"]:checked {
  border-color: #127C96; /* Set border color when checked */
}

/* Change text color when checked */
.radioGroup input[type="radio"]:checked + label {
  color: #127C96;
}


/* AddUser.module.css */

.error {
color: red;
font-size: 12px;
font-weight: 500;
/* padding-bottom: 200px; */
}

.errorInput {
border-color: red !important;
}

.selectDropdown {
  width: 100%;          /* Ensure dropdowns take full width */
  max-height: 200px;    /* Set maximum height */
  overflow-y: auto;     /* Enable scrolling if content is too long */
}

.reactSelectWrapper {
  width: 100%;
  color: #001b36;

}

.reactSelect :global(.react-select__control) {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 2px 5px;
  font-size: 14px;
  color: black;
  min-height: 38px;
}

.reactSelect :global(.react-select__menu) {
  color: #001b36;
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.reactSelect :global(.react-select__option) {
  color: #001b36;
  font-size: 14px;
  padding: 8px 12px;
}

.form {
  width: 100%;
  max-width: 900px;
  background-color: #fff;
  color: #001b36;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  /* max-height: calc(100vh - 120px); */ /* Remove this */
}

@media (max-width: 768px) {
  .form {
      padding: 20px;
      max-width: 95%;
  }
}

@media (max-width: 480px) {
  .form {
      padding: 15px;
  }
}

.row {
  
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

/* Hide the number input spinner (increment/decrement arrows) */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}

.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

/* Checkbox style */
.row input[type="radio"] {
  appearance: none;
  color: #127C96;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #127C96; /* Set the border to your primary color */
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white; /* White background for the checkbox */
  background-image: none; /* Ensure no native check icon appears */
}

.row input[type="radio"]:checked {
  background-color: #127C96; /* Set the background color to your primary color when checked */
  border-color: #127C96; /* Ensure the border color is also your primary color */
}

.row input[type="radio"]:checked::before {
  content: '\2713'; /* Unicode for check mark */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff; /* Tick color changed to primary */
  font-size: 14px;
  font-weight: bold;
}

.row input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.6); /* White-colored focus ring */
}

.inlineRow {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.inlineRow .row {
  width: 48%;
  padding-right: 10px;
}

.inlineRow .row:last-child {
  padding-right: 0;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

.submitRow {
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
  gap: 10px; /* Space between buttons */
  margin-top: 20px;
  flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
}


/* Primary Button - Submit, Save, etc. */
.primaryBtn {
  background-color: #127C96;  /* Primary color */
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0F6A83;  /* Slightly darker teal on hover */
}

/* Cancel Button - Grey */
.cancelBtn {
  background-color: #E0E0E0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #CCCCCC;  /* Darker grey on hover */
}


.passwordWrapper {
  position: relative;
}

.passwordWrapper input {
  padding-right: 40px; /* Add space for icon */
}

.icon {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
  color: #888;
  font-size: 18px;
}