import Api from '../Api';

// Fetch questions with pagination
export const fetchQuestions = async (page, itemsPerPage) => {
  try {
    const userId = sessionStorage.getItem('userId');
    if (!userId) {
      throw new Error('User ID not found. Please log in again.');
    }

    const offset = (page - 1) * itemsPerPage;
    const requestData = {
      userID: parseInt(userId),
      page: {
        offset,
        fetch: itemsPerPage
      }
    };

    const response = await Api.post('question/getbyuserid', requestData);
    return response.data;
  } catch (error) {
    console.error('Error fetching questions:', error);
    throw error;
  }
};

// Search questions with pagination
export const searchQuestions = async (searchText, page, itemsPerPage) => {
  try {
    const userId = sessionStorage.getItem('userId');
    if (!userId) {
      throw new Error('User ID not found. Please log in again.');
    }

    const offset = (page - 1) * itemsPerPage;
    const requestData = {
      userID: parseInt(userId),
      searchText: searchText.trim(),
      page: {
        offset,
        fetch: itemsPerPage
      }
    };

    const response = await Api.post('question/search', requestData);
    return response.data;
  } catch (error) {
    console.error('Error searching questions:', error);
    throw error;
  }
};

// Delete a question set
export const deleteQuestion = async (preparationId) => {
  try {
    const userId = sessionStorage.getItem('userId');
    if (!userId) {
      throw new Error('User ID not found. Please log in again.');
    }

    const response = await Api.post('question/delete', {
      preparationID: preparationId,
      modifiedBy: userId
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting question:', error);
    throw error;
  }
};