.mainContentBg {
  height: 100%;
  width: 100%;
  background: #f4f7fa;
  padding: 40px 0;
  padding-left: 260px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.formCard, .questionPaperSection {
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.formCard {
  width: 100%;
  max-width: 1000px;
  margin-top: 2rem;
  background: #fff;
  padding: 2.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  color: #222;
  flex-shrink: 0;
}

.formTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 1.8rem;
  margin-bottom: 1.5rem;
}

.formRow {
  display: flex;
  align-items: flex-start;
  gap: 2.2rem;
  color: #222;
  margin-bottom: 0.8rem;
}

.fieldLabel {
  width: 220px;
  font-weight: 600;
  color: #000000;
  padding-top: 0.4rem;
}

.fieldHelper {
  font-size: 0.95rem;
  color: #888;
  margin-top: 0.2rem;
}

.required {
  color: #e53935;
  margin-left: 2px;
}

.inputCell {
  flex: 1;
  color: #222;
}

input[type="text"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1.5px solid #127C96;
  border-radius: 5px;
  font-size: 1rem;
  background: #fafbfc;
  color: #222;
}

input[type="text"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  border: 2px solid #127C96;
  outline: none;
  background: #fff;
}

textarea {
  resize: vertical;
  height: 100px;
}

.optionRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.optionRow input {
  flex: 1;
}

.optionRow button {
  padding: 0.3rem 0.7rem;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.addBtn {
  background-color: #2196f3;
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 0.3rem 0.7rem;
  margin-top: 0.5rem;
  cursor: pointer;
}

.addOptionButton {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s;
  margin-top: 0.5rem;
}

.addOptionButton:hover {
  background-color: #0f6a83;
}

.charCount {
  font-size: 12px;
  color: #888;
  margin-left: 8px;
}

.buttonContainer {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 2rem;
  padding: 0 20px;
}

.actionButton {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButtonCancel {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #d0d0d0;
  color: #000000;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButton:hover {
  background-color: #0f6a83;
}

.actionButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Keep the type selector for backward compatibility or other buttons */
button[type="submit"] {
  /* Inherit styles from .actionButton or keep specific overrides if needed */
}

.formSection {
  background: #fff;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  margin-bottom: 2rem;
  /* border-top: 5px solid #127C96; */
}

.formSection h3 {
  color: #000000;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.questionForm {
  border: 1px solid #e0e0e0;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  background-color: #f9f9f9; /* Slightly different background for clarity */
  position: relative; /* Needed for absolute positioning of remove button */
}

.questionForm .questionText label,
.questionForm .optionsList label,
.questionForm .correctAnswer label,
.questionForm .mandatory,
.questionForm .marks label {
   font-weight: 600;
   color: #000000;
   margin-bottom: 0.5rem;
   display: block;
}

.questionForm .optionsList input[type="text"],
.questionForm .correctAnswer select {
  width: calc(100% - 100px); /* Adjust width considering remove button */
  display: inline-block; /* Allow button next to it */
  margin-right: 10px;
}

.questionForm .optionItem {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
}

.questionForm .optionItem input[type="text"] {
  flex-grow: 1;
  margin-right: 10px;
}

.questionForm .optionItem button {
  padding: 0.4rem 0.8rem;
  background-color: #f44336; /* Match Save Questions button */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
}

.questionForm .optionItem button:hover {
   background-color: #0f6a83; /* Match Save Questions button hover */
}

.questionForm .correctAnswer select {
   width: 100%;
}

.questionForm .mandatory {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.questionForm .marks {
   margin-bottom: 1rem;
}

.questionForm .actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.questionForm .actions button {
   padding: 0.4rem 0.8rem;
   background-color: #f44336;
   color: white;
   border: none;
   border-radius: 5px;
   cursor: pointer;
   font-size: 0.9rem;
}

.questionForm .actions button:hover {
    background-color: #d32f2f;
}

.addQuestion {
  text-align: center;
  margin-top: 2rem;
}

.addQuestion button {
  padding: 0.7rem 1.2rem;
  font-size: 1rem;
  background-color: #127C96; /* Match Save Questions button */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s;
}

.addQuestion button:hover {
  background-color: #0f6a83; /* Match Save Questions button hover */
}

.questionPaperSection {
  width: 100%;
  max-width: 1000px;
  margin: 1.5rem auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  padding: 1.5rem 1.5rem;
  color: #222;
  flex-shrink: 0;
}

.questionPaperTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #000000;
  margin-bottom: 1.2rem;
  text-align: center;
}

.questionCard {
  margin-bottom: 1.2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
  color: #222;
}

.questionText {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #222;
}

.optionsList {
  margin-left: 1.2rem;
  margin-bottom: 0.5rem;
  color: #222;
}

.optionItem {
  margin-bottom: 0.2rem;
  color: #444;
}

.correctOption {
  color: #000000;
  font-weight: 600;
}


@media (max-width: 900px) {
  .mainContentBg {
    padding-left: 0;
    padding: 16px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

@media (max-width: 700px) {
  .formCard {
    padding: 1.2rem 0.5rem;
    margin: 0 8px;
  }
  .mainContentBg {
    padding: 16px;
  }
  .formRow {
    flex-direction: column;
    gap: 0.5rem;
  }
  .fieldLabel {
    width: 100%;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  text-align: center;
  color: #222;
}

.modalContent h3 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.reasonTextarea {
  width: 100%;
  padding: 10px;
  border: 1.5px solid #127C96;
  border-radius: 5px;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 20px;
  box-sizing: border-box;
}

.modalButtons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.modalButtons .actionButton {
    min-width: 100px;
}

.cancelButton {
    padding: 12px 25px;
    font-size: 1rem;
    background-color: #e0e0e0;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.cancelButton:hover {
    background-color: #d5d5d5;
    border-color: #b0b0b0;
}
