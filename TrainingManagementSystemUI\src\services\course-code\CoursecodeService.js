import Api from '../Api';

// Fetch course codes by user ID
export const fetchCourseCodesByUserId = async (payload) => {
  try {
    const response = await Api.post('/coursecode/getbyuserid', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Get course code by ID
export const getCourseCodeById = async (courseCodeID) => {
  try {
    const response = await Api.get(`/coursecode/${courseCodeID}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Create new course code
export const createCourseCode = async (payload) => {
  try {
    const response = await Api.post('/coursecode/upsert', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Update course code
export const updateCourseCode = async (payload) => {
  try {
    const response = await Api.post('/coursecode/upsert', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Delete/Deactivate course code
export const deleteCourseCode = async (courseCodeID, modifiedBy) => {
  try {
    const response = await Api.post('/coursecode/delete', {
      courseCodeID,
      modifiedBy
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Update course code status
export const updateCourseCodeStatus = async ({ courseCodeID, courseCodeStatus, modifiedBy, reasonForChange }) => {
  try {
    const response = await Api.post('/coursecode/updatestatus', {
      courseCodeID,
      courseCodeStatus,
      modifiedBy,
      reasonForChange
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Fetch approved documents and OJTs
export const fetchApprovedDocumentsAndOjts = async (payload) => {
  try {
    const response = await Api.post('/document/getallapproved', payload);
    if (response.data && response.data.header && response.data.header.errorCount === 0) {
      const formattedDocuments = response.data.approvedDocuments.map(item => ({
        value: item.documentID !== 0 ? `doc-${item.documentID}` : `ojt-${item.ojtid}`,
        label: item.name,
        description: item.code,
        type: item.type,
        departmentName: item.departmentName || 'Other'
      }));

      return {
        header: response.data.header,
        approvedDocuments: formattedDocuments || [],
        totalRecord: response.data.totalRecord || 0,
      };
    }
    return {
      header: response.data?.header || { errorCount: 1, messages: [{ messageText: 'Failed to fetch approved documents' }] },
      approvedDocuments: [],
      totalRecord: 0,
    };
  } catch (error) {
    console.error('Error in fetchApprovedDocumentsAndOjts:', error);
    return {
      header: {
        errorCount: 1,
        messages: [{ messageText: error.message || 'An unexpected error occurred' }]
      },
      approvedDocuments: [],
      totalRecord: 0,
    };
  }
};
