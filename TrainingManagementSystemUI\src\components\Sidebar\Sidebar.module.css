/* ---------- Sidebar Base ---------- */
.sidebar {
  width: 16rem;
  background-color: #127C96;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  padding-top: 1rem;
  transition: transform 0.3s ease-in-out;
  transform: translateX(0);
  z-index: 1000;
}

/* Hidden by default on mobile */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .mobileToggle {
    display: block;
  }
}

/* ---------- Mobile Toggle Button ---------- */
.mobileToggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1100;
  background-color: #1995AD;
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  display: none;
}

.mobileToggle svg {
  width: 24px;
  height: 24px;
}

/* ---------- Logo ---------- */
.logoContainer {
  background-color: white;
  border-radius: 10px;
  width: 9em;
  height: 3.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.logo {
  width: 80%;
  height: auto;
  object-fit: contain;
}

/* ---------- Sidebar Top Section ---------- */
.sidebarTop {
  padding: 0 1rem 1rem 1rem;
}

/* ---------- Menu List ---------- */
.menuList {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* ---------- Menu Item ---------- */
.menuItem {
  background-color: #127C96;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: background-color 0.3s ease, transform 0.2s ease, color 0.3s ease;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.096);
  color: #e2f9f9d2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menuItem:hover {
  background-color: #1DA7C2;
  color: #ffffff;
}

/* ---------- Active Menu Item ---------- */
.activeMenuItem {
  background-color: #1995AD;
  font-weight: 600;
  color: #ffffff;
  pointer-events: none;
}

/* ---------- Open Menu Item ---------- */
.openMenuItem {
  background-color: #1995AD;
  color: #ffffff;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ---------- Arrow Icon ---------- */
.arrowIcon {
  display: inline-block;
  transition: transform 0.3s ease;
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

.rotate {
  transform: rotate(90deg);
}

/* ---------- Submenu ---------- */
.subMenuList {
  list-style: none;
  padding-left: 1.5rem;
  margin: 0.25rem 0 0.75rem;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  max-height: 1000px;
}

.subMenuItem {
  font-size: 0.8rem;
  padding: 0.45rem 0.75rem;
  margin-bottom: 0.2rem;
  color: #e2f9f9d2;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.subMenuItem:hover:not(.activeSubMenuItem) {
  background-color: #1DA7C2;
  color: white;
}

/* ---------- Active Submenu Item ---------- */
.activeSubMenuItem {
  background-color: #1995AD;
  color: #ffffff;
  font-weight: 500;
}

/* ---------- Scrollable Menu Section ---------- */
.menuScroll {
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
  padding-right: 0.5rem;
  scrollbar-width: thin;
}

.menuScroll::-webkit-scrollbar {
  width: 6px;
}

.menuScroll::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* ---------- Bottom Section ---------- */
.sidebarBottom {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: auto;
}

.avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
}

.userName {
  font-size: 0.875rem;
  color: white;
  font-weight: 500;
}

.logoutBtn {
  background: transparent;
  color: #a0aec0;
  cursor: pointer;
  font-size: 0.75rem;
  padding: 0;
  margin-top: 0.25rem;
}