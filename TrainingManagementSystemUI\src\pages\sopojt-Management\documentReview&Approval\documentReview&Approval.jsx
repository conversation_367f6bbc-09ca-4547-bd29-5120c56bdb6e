import React, { useState, useEffect, useRef } from 'react';
import styles from './documentReview&Approval.module.css';
import Sidebar from '../../../components/Sidebar/Sidebar';
import Navbar from '../../../components/navbar/Navbar';
import { FaEye, FaCheck, FaUndo, FaTimes, FaDownload, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import Pagination from '../../../components/pagination/Pagination';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';
import { fetchPendingDocuments, updateDocumentStatus } from '../../../services/sopojt-Management/documentReview&ApprovalService';
import { downloadFileToBrowserById } from '../../../services/DownloadService';
import FileViewer from '../../../components/common/fileViewer/FileViewer';

const itemsPerPage = 10;

const DocumentReviewApproval = () => {
  const [documents, setDocuments] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [selectedDoc, setSelectedDoc] = useState(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionRemarks, setRejectionRemarks] = useState('');
  const [approvalRemarks, setApprovalRemarks] = useState('');
  const [showLeftIndicator, setShowLeftIndicator] = useState(false);
  const [showRightIndicator, setShowRightIndicator] = useState(true);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [returnRemarks, setReturnRemarks] = useState('');
  const [showFileViewer, setShowFileViewer] = useState(false);
  const [pdfFileId, setPdfFileId] = useState(null);
  const [pdfFileType, setPdfFileType] = useState(null);
  const [pdfFileExtension, setPdfFileExtension] = useState(null);
  const tableContainerRef = useRef(null);

  const checkScroll = () => {
    if (tableContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = tableContainerRef.current;
      const isAtStart = scrollLeft <= 0;
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 1;
      
      setShowLeftIndicator(!isAtStart);
      setShowRightIndicator(!isAtEnd);
    }
  };

  const handleScroll = (direction) => {
    if (tableContainerRef.current) {
      const scrollAmount = 200;
      const currentScroll = tableContainerRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      tableContainerRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener('scroll', checkScroll);
      window.addEventListener('resize', checkScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
        window.removeEventListener('resize', checkScroll);
      }
    };
  }, []);

  useEffect(() => {
    loadDocuments();
  }, [currentPage, searchTerm]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const response = await fetchPendingDocuments();
      
      if (response.header.errorCount === 0) {
        const filteredDocs = response.documentMasters.filter(doc => 
          doc.documentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.documentCode.toLowerCase().includes(searchTerm.toLowerCase())
        );
        
        setDocuments(filteredDocs);
        setTotalRecords(filteredDocs.length);
      } else {
        toast.error(response.header.messages[0].messageText);
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleViewDocument = (doc) => {
    setPdfFileId(doc.documentID);
    setPdfFileType('document');
    setPdfFileExtension(doc.documentExtention);
    setShowFileViewer(true);
  };

  const handleDownloadDocument = async (documentId) => {
    try {

      await downloadFileToBrowserById('document',documentId);

    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  const handleApprove = (doc) => {
    setSelectedDoc(doc);
    setShowApproveModal(true);
  };

  // Unified status update handler
  const handleStatusUpdate = async (status, remarks = '') => {
    if ((status === 'Rejected' || status === 'Returned' || status === 'Approved') && !remarks.trim()) {
      toast.error(`Please provide ${status.toLowerCase()} remarks`);
      return;
    }

    try {
      const response = await updateDocumentStatus({
        documentID: selectedDoc.documentID,
        documentStatus: status,
        modifiedBy: sessionStorage.getItem('userID'),
        ...(remarks && { reasonForChange: remarks })
      });

      if (response.header.errorCount === 0) {
        const message = response?.header?.messages?.[0]?.messageText ?? `Document ${status.toLowerCase()}ed successfully`;
        toast.success(message);
        if (status === 'Rejected') {
          setShowRejectModal(false);
          setRejectionRemarks('');
        } else if (status === 'Returned') {
          setShowReturnModal(false);
          setReturnRemarks('');
        } else {
          setShowApproveModal(false);
        }
        setSelectedDoc(null);
        loadDocuments();
      } else {
        const errorMessage = response?.header?.messages?.[0]?.messageText ?? `Failed to ${status.toLowerCase()} document`;
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error(`Error ${status.toLowerCase()}ing document:`, error);
      toast.error(`Failed to ${status.toLowerCase()} document`);
    }
  };

  const handleReturn = (doc) => {
    setSelectedDoc(doc);
    setShowReturnModal(true);
  };

  const handleReject = (doc) => {
    setSelectedDoc(doc);
    setShowRejectModal(true);
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const getCurrentItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return documents.slice(startIndex, endIndex);
  };

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      {showApproveModal && (
        <Modal
          title="Confirm Approval"
          message={
            <div>
              <p>Are you sure you want to approve the document "{selectedDoc?.documentName}" ({selectedDoc?.documentCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="approveReason">Reason for approval:</label>
                <textarea
                  id="approveReason"
                  value={approvalRemarks}
                  onChange={(e) => setApprovalRemarks(e.target.value)}
                  placeholder="Please provide a reason for approval"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Approved', approvalRemarks)}
          onCancel={() => {
            setShowApproveModal(false);
            setSelectedDoc(null);
            setApprovalRemarks('');
          }}
        />
      )}

      {showRejectModal && (
        <Modal
          title="Confirm Rejection"
          message={
            <div>
              <p>Are you sure you want to reject the document "{selectedDoc?.documentName}" ({selectedDoc?.documentCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="rejectReason">Reason for rejection:</label>
                <textarea
                  id="rejectReason"
                  value={rejectionRemarks}
                  onChange={(e) => setRejectionRemarks(e.target.value)}
                  placeholder="Please provide a reason for rejection"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Rejected', rejectionRemarks)}
          onCancel={() => {
            setShowRejectModal(false);
            setSelectedDoc(null);
            setRejectionRemarks('');
          }}
        />
      )}

      {showReturnModal && (
        <Modal
          title="Confirm Return"
          message={
            <div>
              <p>Are you sure you want to return the document "{selectedDoc?.documentName}" ({selectedDoc?.documentCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="returnReason">Reason for return:</label>
                <textarea
                  id="returnReason"
                  value={returnRemarks}
                  onChange={(e) => setReturnRemarks(e.target.value)}
                  placeholder="Please provide a reason for return"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Returned', returnRemarks)}
          onCancel={() => {
            setShowReturnModal(false);
            setSelectedDoc(null);
            setReturnRemarks('');
          }}
        />
      )}

      {showFileViewer && (
        <FileViewer
          id={pdfFileId}
          type={pdfFileType}
          extension={pdfFileExtension}
          onClose={() => setShowFileViewer(false)}
        />
      )}

      <div className={styles.container}>
        <div className={styles.documentReview}>
          <div className={styles.panelHeader}>
            <h2>Document Approval</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
            </div>
          </div>

          <div ref={tableContainerRef} className={styles.docTableContainer}>
            
            <div className={styles.tableWrapper}>
              <table className={styles.docTable}>
                <thead>
                  <tr>
                    <th>Document Name</th>
                    <th>Document Code</th>
                    <th>Document Type</th>
                    <th>Version</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="5" className={styles.spinnerCell}>
                        <div className={styles.spinner}></div>
                      </td>
                    </tr>
                  ) : getCurrentItems().length > 0 ? (
                    getCurrentItems().map((doc) => (
                      <tr key={doc.documentID}>
                        <td>{doc.documentName}</td>
                        <td>{doc.documentCode}</td>
                        <td>{doc.documentType}</td>
                        <td>{doc.documentVersion}</td>
                        <td>
                          <div className={styles.actions}>
                            <div className={styles.actionGroup}>
                              <button
                                className={styles.actionButton}
                                onClick={() => handleViewDocument(doc)}
                                title="View Document"
                              >
                                <FaEye className={styles.viewIcon} />
                                <span>View</span>
                              </button>

                              <button
                                className={styles.actionButton}
                                onClick={() => handleDownloadDocument(doc.documentID)}
                                title="Download Document"
                              >
                                <FaDownload className={styles.downloadIcon} />
                                <span>Download</span>
                              </button>
                            </div>
                            <span className={styles.actionDivider}></span>
                            <div className={styles.actionGroupSmall}>
                              <button
                                className={styles.actionButton}
                                onClick={() => handleApprove(doc)}
                                title="Approve Document"
                              >
                                <FaCheck className={styles.approveIcon} />
                                <span>Approve</span>
                              </button>
                              <button
                                className={styles.actionButton}
                                onClick={() => handleReturn(doc)}
                                title="Return Document"
                              >
                                <FaUndo className={styles.returnIcon} />
                                <span>Return</span>
                              </button>
                              <button
                                className={styles.actionButton}
                                onClick={() => handleReject(doc)}
                                title="Reject Document"
                              >
                                <FaTimes className={styles.rejectIcon} />
                                <span>Reject</span>
                              </button>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" style={{ textAlign: 'center', padding: '20px' }}>
                        No documents found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />

        </div>
      </div>
    </>
  );
};

export default DocumentReviewApproval;
