import React from 'react';
import styles from './StatusChip.module.css';

const StatusChip = ({ status }) => {
  const getStatusColor = (status) => {
    switch ((status || '').toLowerCase()) {
      case 'completed':
        return styles.completed;
      case 'overdue':
        return styles.overdue;
      case 'inprogress':
      case 'in progress':
        return styles.inProgress;
      case 'assigned':
        return styles.assigned;
      case 'approved':
        return styles.completed;
      case 'rejected':
        return styles.overdue;
      case 'returned':
        return styles.inProgress;
      case 'underreview':
      case 'under review':
        return styles.default;
      default:
        return styles.default;
    }
  };

  return (
    <span className={`${styles.chip} ${getStatusColor(status)}`}>
      {status}
    </span>
  );
};

export default StatusChip; 