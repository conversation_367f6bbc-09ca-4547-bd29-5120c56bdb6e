import React, { createContext, useState, useCallback, useContext } from 'react';
import { getCourseCodeById } from '../../services/course-code/CoursecodeService';

export const CourseContext = createContext();

export const CourseProvider = ({ children }) => {
  const [courseData, setCourseData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchCourseById = useCallback(async (courseId) => {
    setLoading(true);
    try {
      const data = await getCourseCodeById(courseId);
      if (data.header.errorCount === 0) {
        setCourseData(data.coursecode);
        setError(null);
      } else {
        setError(data.header.messages[0]?.messageText || 'Failed to fetch course data');
        setCourseData(null);
      }
    } catch (err) {
      setError('Failed to fetch course data');
      setCourseData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearCourseData = useCallback(() => {
    setCourseData(null);
    setError(null);
  }, []);

  const value = {
    courseData,
    loading,
    error,
    fetchCourseById,
    clearCourseData,
    setError
  };

  return (
    <CourseContext.Provider value={value}>
      {children}
    </CourseContext.Provider>
  );
};

export const useCourse = () => {
  const context = useContext(CourseContext);
  if (!context) {
    throw new Error('useCourse must be used within a CourseProvider');
  }
  return context;
};

// Export the context and provider
export default CourseContext;
