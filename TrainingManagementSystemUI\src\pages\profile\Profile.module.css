.container {
  padding: 1rem;
  height: 100vh;
  width: 100vw;
  padding-top: 5rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.profileContainer {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  margin-left: 16rem;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 5rem);
  overflow: hidden;
}

.tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 1rem;
}

.tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.tab:hover {
  background-color: #f0f0f0;
  color: #333;
}

.activeTab {
  background-color: #127C96;
  color: white;
}

.activeTab:hover {
  background-color: #127C96;
  color: white;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.detailsSection {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.detailsSection h3 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detailItem label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.detailItem span {
  color: #333;
  font-size: 1rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.1rem;
}

.noData {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.plantTable {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
}

.plantTable th, .plantTable td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.plantTable th {
  background-color: #127C96;
  color: white;
  font-weight: 600;
}

.plantTable tr:nth-child(even) {
  background-color: #f8f9fa;
}

.plantTable tr:last-child td {
  border-bottom: none;
}

@media (max-width: 768px) {
  .profileContainer {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .tabs {
    flex-wrap: wrap;
  }

  .tab {
    flex: 1;
    min-width: 120px;
    text-align: center;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }

  .plantTable th, .plantTable td {
    padding: 0.5rem 0.5rem;
    font-size: 0.95rem;
  }
} 