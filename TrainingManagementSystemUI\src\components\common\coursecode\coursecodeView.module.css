.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.viewModal {
  background: white;
  border-radius: 8px;
  padding: 0 20px 20px 20px;
  width: 60%;
  min-width: 600px;
  height: 80vh;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  padding-right: 40px;
}

.modalHeader h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.5px;
  font-family: 'Segoe UI', sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 50px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleText {
  font-size: 20px;
  font-weight: 700;
}

.codeText {
  font-size: 16px;
  font-weight: 500;
  color: #666;
}

.closeButton {
  position: absolute;
  right: 20px;
  top: 20px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  padding: 0;
  line-height: 1;
  z-index: 2;
}

.closeButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

.closeButton:active {
  transform: scale(0.95);
}

.viewModalContent {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0 20px;
}

.detailBox {
  padding: 12px 0;
  display: flex;
  align-items: center;
  margin-left: 40px;
}

.detailBox h3 {
  margin: 0;
  color: #000;
  font-size: 15px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Segoe UI', sans-serif;
  min-width: 120px;
  text-align: left;
  padding-right: 10px;
}

.detailBox span {
  color: #000;
  font-size: 15px;
  font-weight: 700;
  padding: 0 10px;
  min-width: 20px;
  text-align: center;
}

.detailBox p {
  margin: 0;
  color: #000;
  font-size: 13px;
  line-height: 1.5;
  padding-left: 10px;
  min-width: 200px;
}

/* Status specific styles */
.detailBox:has(h3:contains('Status')) p {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #000;
}

/* Status colors */
.detailBox:has(h3:contains('Status')) p:contains('UnderReview') {
  background-color: #f0f0f0;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Approved') {
  background-color: #e6f4ea;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Returned') {
  background-color: #fff3cd;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Rejected') {
  background-color: #f8d7da;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Draft') {
  background-color: #f0f0f0;
  border: 1px solid #127C96;
}

/* Undefined status */
.detailBox:has(h3:contains('Status')) p:not(:contains('UnderReview')):not(:contains('Approved')):not(:contains('Returned')):not(:contains('Rejected')):not(:contains('Draft')) {
  background-color: #f0f0f0;
  border: 1px solid #127C96;
}

.linkedDocumentsSection {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #127C96;
}

.linkedDocumentsSection h3 {
  margin: 0 0 20px 40px;
  color: #000;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Segoe UI', sans-serif;
}

.linkedDocumentsContainer {
  display: flex;
  flex-direction: column;
  /* gap: 20px; */
}

.documentSection {
  background: #fff;
  border-radius: 6px;
  margin-bottom: 20px;
}

.documentSection h4 {
  margin: 0 0 15px 40px;
  color: #000;
  font-size: 15px;
  font-weight: 700;
  padding: 8px 0 8px 20px;
  border-bottom: 1px solid #127C96;
  font-family: 'Segoe UI', sans-serif;
}

.linkedDocumentsList {
  list-style: none;
  padding: 0;
  margin: 0 0 0 40px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 300px;
}

.documentItem {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #127C96;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  max-width: 300px;
}

.documentInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* gap: 8px; */
  width: 100%;
  border-left: 1px solid #127C96;
  padding-left: 8px;
}

.documentName {
  color: #000;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.documentCode {
  color: #000;
  font-size: 12px;
  font-weight: 400;
  margin-left: 4px;
  white-space: nowrap;
}

.documentFrequency {
  color: #000;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 8px;
  background: rgba(18, 124, 150, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

.noDocuments {
  color: #000;
  font-style: italic;
  margin: 0;
  padding: 10px 0;
} 