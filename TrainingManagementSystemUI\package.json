{"name": "TrainingManagementSystemUI", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "antd": "^5.25.0", "axios": "^1.8.4", "bootstrap": "^5.3.5", "classnames": "^2.5.1", "country-codes-list": "^2.0.0", "flowbite": "^3.1.2", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-resizable": "^3.0.5", "react-router-dom": "^6.30.0", "react-select": "^5.10.1", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "vite": "^6.2.5"}}