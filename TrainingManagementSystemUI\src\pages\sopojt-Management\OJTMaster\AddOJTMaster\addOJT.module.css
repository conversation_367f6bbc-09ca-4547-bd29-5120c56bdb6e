.container {
  padding: 1rem;
  height: 100vh;
  width: 100vw;
  padding-top: 2rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow-y: auto; /* Allow scrolling if content overflows */
}

.formCard {
  max-width: 1000px;
  margin: 0 0 0 25%; /* Adjust for sidebar */

  margin-right: auto;
  background: #fff;
  padding: 2.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* Increased gap for better spacing */
  color: #222;
  margin-bottom: 2%;
}


.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.formTitle {
  text-align: center;
  color: #127C96;
  margin-bottom: 1.5rem;
  font-size: 1.8rem; /* Larger title */
}

.formSection {
  background: #fff;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  /* border-top: 5px solid #127C96;  */
}

.formSection h3 {
  color: #000000;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.formRow {
  display: flex;
  align-items: flex-start;
  gap: 2rem; /* Adjusted gap */
  margin-bottom: 1.2rem; /* Adjusted bottom margin */
}

.fieldLabel {
  width: 250px; /* Adjusted label width */
  font-weight: 600;
  color: #000000; /* Adjusted color */
  padding-top: 0.5rem; /* Adjusted padding */
}

.required {
  color: #e53935;
  margin-left: 2px;
}

.inputCell {
  flex: 1;
}

input[type="text"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 0.6rem;
  border: 1px solid #ccc; /* Adjusted border */
  border-radius: 5px;
  font-size: 1rem;
  background: #fff; /* Adjusted background */
  color: #222;
  box-sizing: border-box; /* Include padding and border in element's total width and height */
}

input[type="text"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  border-color: #127C96; /* Adjusted focus border color */
  outline: none;
  box-shadow: 0 0 5px rgba(18, 124, 150, 0.3); /* Added subtle shadow on focus */
}

textarea {
  resize: vertical;
  min-height: 80px; /* Adjusted minimum height */
}

/* Styling for the dynamic Activity Details section */
.activityDetailsContainer {
    margin-top: 2rem; /* Space above the dynamic section */
    border-top: 1px dashed #ccc; /* Separator line */
    padding-top: 1.5rem; /* Space below the separator */
}

.activityDetailRow {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 1.2rem;
}

.activityDetailRow .fieldLabel {
     width: 250px; /* Keep consistent label width */
     font-weight: 600;
     color: #196d97;
     padding-top: 0.5rem;
}

.activityDetailRow .inputCell {
    flex: 1;
}

.activityDetailRow textarea {
    width: 100%;
    padding: 0.6rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1rem;
    background: #fff;
    color: #222;
    box-sizing: border-box;
    resize: vertical;
    min-height: 60px; /* Smaller height for activity details */
}

.fileDetails {
    margin-top: 0.8rem;
    padding: 0.8rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    font-size: 0.9rem;
    color: #333;
}

.fileDetails p {
    margin: 0 0 0.4rem 0;
    padding: 0;
}

.fileDetails p:last-child {
    margin-bottom: 0;
}

.validationError {
  color: #e53935;
  font-size: 0.9rem;
  margin-top: 0.4rem;
  display: block; /* Ensure it appears on a new line */
}

.buttonContainer {
  display: flex;
  gap: 15px; /* Adjusted gap */
  justify-content: center;
  margin-top: 2.5rem; /* Adjusted margin */
}

.actionButton {
  padding: 12px 25px; /* Adjusted padding */
  font-size: 1rem; /* Adjusted font size */
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButtonCancel {
  padding: 12px 25px; /* Adjusted padding */
  font-size: 1rem; /* Adjusted font size */
  background-color: #d0d0d0;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}



.actionButton:hover {
  background-color: #0f6a83;
}

.actionButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .formCard {
    padding: 1.5rem 1rem;
    margin-left: 0; /* Remove margin on smaller screens */
  }

  .formRow {
    flex-direction: column; /* Stack elements vertically */
    gap: 0.5rem;
  }

  .fieldLabel,
  .activityDetailRow .fieldLabel {
    width: 100%; /* Full width on smaller screens */
    padding-top: 0;
  }

  .inputCell,
  .activityDetailRow .inputCell {
    flex: none; /* Remove flex-grow */
    width: 100%; /* Full width */
  }
}

.removeActivityDetailButton {
    background-color: #f44336; /* Red background */
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.4rem 0.8rem; /* Adjusted padding */
    cursor: pointer;
    margin-left: 10px; /* Space from the textarea */
    flex-shrink: 0; /* Prevent shrinking */
    font-size: 0.9rem; /* Adjusted font size */
    transition: background-color 0.2s ease;
}

.removeActivityDetailButton:hover {
    background-color: #d32f2f; /* Darker red on hover */
}

/* Custom File Input Styling */
.customFileInput {
    display: inline-block;
}

.customFileInput input[type="file"] {
    display: none; /* Hide the default input */
}

.chooseFileButton {
    background-color: #4d5a68; /* Dark background color from image */
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-right: 10px;
    transition: background-color 0.2s ease;
}

.chooseFileButton:hover {
    background-color: #3a4552; /* Darker shade on hover */
}

.fileNameDisplay {
    display: inline-block;
    font-size: 1rem;
    color: #555; /* Adjust color as needed */
}

.fileInfoText {
    font-size: 0.85rem;
    color: #888;
    margin-top: 0.5rem; /* Adjusted margin to match RegisterDocument */
}

.smallInput {
  width: 50px;
  font-size: 0.5rem;
  padding: 0.4rem;
}

.smallPlaceholder::placeholder {
  font-size: 0.5rem;
}

.durationInput {
  width: 45px !important;
  padding: 4px 6px !important;
  font-size: 0.9rem !important;
  text-align: center;
}

.durationInput::placeholder {
  font-size: 0.8rem;
  color: #999;
  font-size: 0.2rem;

}

.durationLabel {
  font-size: 0.9rem;
  color: #666;
  margin: 0 2px;
}
