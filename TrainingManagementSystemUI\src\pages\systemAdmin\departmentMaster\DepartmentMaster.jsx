import React, { useState, useEffect, useContext } from "react";
import styles from "./DepartmentMaster.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { FaEdit } from "react-icons/fa";
import { FcCancel } from "react-icons/fc";
import Modal from "../../../components/common/Modal";
import { useNavigate } from "react-router-dom";
import { DepartmentContext } from "../../../context/DepartmentContext";
import {
  fetchAllDepartments,
  deleteDepartment,
} from "../../../services/systemAdmin/DepartmentMasterService";
import Pagination from "../../../components/pagination/Pagination";

const DepartmentMaster = () => {
  const navigate = useNavigate();
  const [departments, setDepartments] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const itemsPerPage = 10;
  const { setDepartmentDetails } = useContext(DepartmentContext);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Debounce searchTerm input
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on new search
    }, 500); // 500ms debounce delay

    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  useEffect(() => {
    const loadDepartments = async () => {
      try {
        setIsLoading(true);
        const data = await fetchAllDepartments(currentPage, itemsPerPage, debouncedSearchTerm);

        if (data.header?.errorCount === 0 && Array.isArray(data.departments)) {
          setDepartments(data.departments);
          setTotalRecords(data.totalRecord || 0);
        } else {
          toast.error(data.header?.messages?.[0]?.messageText || "Failed to load departments");
        }
      } catch (error) {
        console.error("Error fetching departments:", error);
        toast.error("An error occurred while fetching departments");
      } finally {
        setIsLoading(false);
      }
    };

    loadDepartments();
  }, [currentPage, debouncedSearchTerm, refreshKey]);

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const navigateTo = (path, state = {}) => {
    navigate(path, { state });
  };

  const handleEditDepartment = (department) => {
    setDepartmentDetails(department.departmentID, department.departmentName);
    navigateTo("/system-admin/department-master/edit-department", {
      departmentData: department,
    });
  };

  const handleDeleteDepartment = (department) => {
    setSelectedDepartment(department);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      const res = await deleteDepartment(selectedDepartment.departmentID);
      if (res.header?.errorCount === 0) {
        toast.success(res.header?.messages?.[0]?.messageText || "Deleted successfully");
        setCurrentPage(1);
        setRefreshKey((prev) => prev + 1);
      } else {
        toast.error(res.header?.messages?.[0]?.messageText || "Delete failed");
      }
    } catch (error) {
      toast.error("Error while deleting department");
      console.error(error);
    } finally {
      setShowDeleteModal(false);
      setSelectedDepartment(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedDepartment(null);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <div className={styles.departmentMaster}>
          <div className={styles.panelHeader}>
            <h2>Department Master</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
              <button
                className={styles.addUserBtn}
                onClick={() =>
                  navigateTo("/system-admin/department-master/add-department")
                }
              >
                + Add
              </button>
            </div>
          </div>

          <div className={styles.departmentTableContainer}>
            <table className={styles.departmentTable}>
              <thead>
                <tr>
                  <th>Department Name</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan="2" style={{ textAlign: "center", padding: "20px" }}>
                      <div className={styles.loader}></div>
                    </td>
                  </tr>
                ) : departments.length > 0 ? (
                  departments.map((item, index) => (
                    <tr key={index}>
                      <td>{item.departmentName}</td>
                      <td className={styles.actions}>
                        <button
                          className={styles.editBtn}
                          onClick={() => handleEditDepartment(item)}
                        >
                          <FaEdit />
                        </button>
                        <button
                          className={styles.editBtn}
                          onClick={() => handleDeleteDepartment(item)}
                        >
                          <FcCancel />
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="2" style={{ textAlign: "center", padding: "20px" }}>
                      No Records found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>

      {showDeleteModal && (
        <Modal
          title="Confirm Delete"
          message={`Are you sure you want to delete department "${selectedDepartment?.departmentName}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
    </>
  );
};

export default DepartmentMaster;
