/* Full-screen centered layout */
.landing-container {
    height: 100vh;
    width: 100vw;
    background-color: #f4f6f8;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    box-sizing: border-box;
    text-align: center;
  }
  
  /* Logo */
  .logo-container {
    margin-bottom: 1rem;
  }
  
  .landing-logo {
    width: 120px;
    height: auto;
  }
  
  /* Title */
  .landing-title {
    font-size: 2.4rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #333;
  }
  
  /* Card wrapper */
  .card-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    max-width: 800px;
    justify-content: center;
    align-items: center;
  }
  
  @media (min-width: 768px) {
    .card-wrapper {
      flex-direction: row;
    }
  }
  
  /* Card styles */
  .landing-card {
    background-color: #ffffff;
    border-radius: 1rem;
    padding: 2rem;
    width: 100%;
    max-width: 350px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 30px 35px rgba(15, 14, 14, 0.08);
    border: 1px solid #e0e0e0;
  }
  
  .landing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
  }
  
  /* Card content */
  .card-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c2c2c;
    margin-bottom: 0.6rem;
  }
  
  .card-description {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.4;
  }
  
  /* Optional hover border color */
  .tms-card:hover {
    border-color: #1e88e5;
  }
  
  .dms-card:hover {
    border-color: #43a047;
  }
  