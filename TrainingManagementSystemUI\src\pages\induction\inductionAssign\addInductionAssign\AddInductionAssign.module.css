.container {
    position: absolute;
    top: 6rem;
    left: 16rem;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    box-sizing: border-box;
    height: calc(100vh - 6rem);
    width: calc(100% - 16rem);
    overflow-y: auto;
  }
  
  .sectionHeading {
    font-size: 22px;
    font-weight: 600;
    color: #00376e;
    margin-top: 10px;
    margin-bottom: 30px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
  }
  
  .form {
    width: 100%;
    max-width: 900px;
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .row {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
  }
  
  .row label {
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: black;
  }
  
  .row input,
  .row select,
  .row textarea {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    width: 100%;
    color: black;
    background-color: white;
  }
  
  .row select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23127C96' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 30px;
  }
  
  .row select:focus {
    outline: none;
    border-color: #127C96;
    box-shadow: 0 0 0 2px rgba(18, 124, 150, 0.1);
  }
  
  .row select option {
    padding: 10px;
    background-color: white;
    color: #333;
  }
  
  .row select option:hover {
    background-color: #f5f5f5;
  }
  
  .submitRow {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  .submitRow button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .submitRow button[type="submit"] {
    background-color: #127C96;
    color: white;
  }
  
  .submitRow button[type="button"] {
    background-color: #ccc;
    color: white;
  }
  
  .submitRow button:hover {
    background-color: #005c6e;
  }

  
  
  @media (max-width: 768px) {
    .form {
      padding: 20px;
      max-width: 95%;
    }
  }
  
  @media (max-width: 480px) {
    .form {
      padding: 15px;
    }
  }