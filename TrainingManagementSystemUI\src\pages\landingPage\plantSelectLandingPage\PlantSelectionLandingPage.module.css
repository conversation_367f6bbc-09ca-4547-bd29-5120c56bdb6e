/* Full-screen centered layout */
.landingContainer {
  min-height: 100vh;
  width: 100vw;
  background-color: #ffffff; /* White background */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 2rem 1rem;
  box-sizing: border-box;
  text-align: center;
  height: 100vh;
}

/* Logo */
.logoContainer {
  margin-bottom: 1rem;
}

.landingLogo {
  margin-top: 5rem;
  width: 160px; /* Increased logo size */
  height: auto;
}

/* Title */
.landingTitle {
  font-size: 2.4rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #333333; /* Dark text for better visibility */
}

/* Select Plant Heading */
.selectPlantHeading {
  font-size: 2rem;
  margin-top: 3rem;
  font-weight: 700;
  margin-bottom: 1rem; /* Space between heading and cards */
  color: #17767f; /* Dark color for visibility */
}


/* Card wrapper container with scroll */
.cardWrapperContainer {
  width: 100%;
  flex-grow: 1; /* Make the container take up the remaining space */
  overflow-y: auto; /* Enable vertical scrolling */
  padding: 1rem;
  box-sizing: border-box;
  margin-top: 2rem;
  max-height: 80vh; /* Set a max height for scrolling */
}

/* Card wrapper */
.cardWrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 1.5rem;
  width: 100%;
  max-width: 1400px;
  box-sizing: border-box;
  padding: 1rem;
}

/* Card styles */
.landingCard {
  background-color: #ffffff;
  border-radius: 1rem;
  padding: 1rem;
  width: 300px;
  height: 150px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.1); /* Default shadow */
  border: 1px solid #e0e0e0;
  text-align: left;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.landingCard:hover {
  /* transform: translateY(-5px); */
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.08); /* Reduced shadow on hover */
}

/* Card content */
.cardTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: #124448; /* White text color for better contrast */
  margin-bottom: 0.5rem;
}

.cardDescription {
  font-size: 0.85rem;
  color: #5c7e7e; /* White text color for better contrast */
  line-height: 1.3;
  flex-grow: 1;
}

/* Specific card background colors (different from primary color) */
.tmsCard {
  background-color: #c0eaea; /* Warm yellowish tone */
}

.dmsCard {
  background-color: #c0eaea; /* Teal blue shade */
}

/* Hover effects for borders */
.tmsCard:hover {
  border-color: #c0eaea; /* Darker orange shade */
}

.dmsCard:hover {
  border-color: #c0eaea; /* Darker teal */
}

/* Tablet view: 2 cards per row */
@media (max-width: 1024px) {
  .landingCard {
    width: 45%; /* Adjust for 2 cards per row */
  }
}

/* Mobile view: 1 card per row */
@media (max-width: 640px) {
  .landingCard {
    width: 90%; /* Adjust for 1 card per row */
    max-width: 320px;
  }
}