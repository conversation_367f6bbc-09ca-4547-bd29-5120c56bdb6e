/* Container Styling */
.container {
  position: absolute;
  top: 6rem;
  left: 16rem;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 40px;
  box-sizing: border-box;
  height: calc(100vh - 6rem);
  width: calc(100% - 16rem);
  background-color: #ffffff;
  color: #000000;
  overflow-y: auto;
}

/* Outer Card Styling */
.outerCard {
  position: relative;  /* Added this to enable absolute positioning of the progress bar */
  width: 100%;
  max-width: 800px;
  min-height: calc(100% - 20%);
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 0 30px 30px 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  margin: auto;
  flex-direction: column;
  height: fit-content;
}


/* Form Container */
.formContainer {
  width: 100%;
  max-width: 500px;
}

/* Form Styling */
.form {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Heading Styling */
.heading {
  font-size: 22px;
  margin-bottom: 20px;
  color: #0A0A0A;
}

/* Customize toast appearance */
.Toastify__toast {
  min-height: 30px !important;  /* default is 48px */
  padding: 6px 12px !important;
  font-size: 14px !important;
  width: 300px; /* optional width constraint */
}

/* Field Styling */
.field {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Label Styling */
label {
  font-weight: 600;
  margin-bottom: 6px;
  color: #0A0A0A;
  font-size: 14px;
}

/* Input Styling */
input {
  width: 100%;
  padding: 8px 36px 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #F1F1F2;
  color: #0A0A0A;
  font-size: 14px;
  box-sizing: border-box;
  max-width: 100%;
}

/* Input Wrapper for Password Fields */
.inputWrapper {
  position: relative;
  width: 100%;
}

.inputWrapper span {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #127C96;
  font-size: 16px;
}

/* Error Styles */
.inputError {
  border-color: red;
}

.errorText {
  color: red;
  font-size: 0.85rem;
  margin-top: 4px;
  text-align: right;
  display: block;
}

/* Submit Button Styling */
.submitBtn {
  padding: 8px 16px;
  font-size: 14px;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  align-self: flex-end;
  transition: background-color 0.3s ease;
  min-width: 100px;
}

.submitBtn:hover {
  background-color: #0a5f6a;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .outerCard {
    padding: 20px;
    min-height: auto;
  }

  .form {
    padding: 0;
    width: 90%;
  }

  .submitBtn {
    width: 100%;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .outerCard {
    padding: 15px;
    min-height: auto;
  }

  .submitBtn {
    padding: 1rem;
  }
}