import React, { useState, useEffect, useContext, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaTimes, FaSearch } from 'react-icons/fa';
import { FcCancel } from 'react-icons/fc';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from '../../../components/pagination/Pagination';
import Modal from '../../../components/common/Modal';
import styles from './questionPrepareGrid.module.css';
import { fetchQuestions, searchQuestions, deleteQuestion } from '../../../services/sopojt-Management/QuestionPrepareGridService';
import { QuestionPrepareContext } from '../../../context/sopOjt-Management/QuestionPrepareContext';

const QuestionPrepareGrid = () => {
  const navigate = useNavigate();
  const { loadQuestionForEdit } = useContext(QuestionPrepareContext);
  const [questions, setQuestions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [error, setError] = useState(null);
  const searchTimeoutRef = useRef(null);

  // Debounce search term
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm]);

  useEffect(() => {
    const loadQuestions = async () => {
      try {
        setLoading(true);
        setError(null);

        let data;
        if (debouncedSearchTerm.trim()) {
          // Use search API when there's a search term
          data = await searchQuestions(debouncedSearchTerm, currentPage, itemsPerPage);
          console.log('Search Results:', data);
        } else {
          // Use regular fetch API when no search term
          data = await fetchQuestions(currentPage, itemsPerPage);
          console.log('Questions Data:', data);
        }

        const message = data.header?.messages?.[0];
        if (message?.messageLevel?.toLowerCase() === 'warning') {
          toast.warning(message.messageText);
        } else if (message?.messageLevel?.toLowerCase() === 'error') {
          toast.error(message.messageText);
        }

        if (data.header?.errorCount === 0 && Array.isArray(data.questions)) {
          setQuestions(data.questions);
          setTotalRecords(data.totalRecord || 0);
        } else {
          console.error('Failed to load questions:', data.header?.messages);
          setQuestions([]);
          setTotalRecords(0);
        }
      } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Error fetching questions');
        setQuestions([]);
        setTotalRecords(0);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    loadQuestions();
  }, [currentPage, debouncedSearchTerm, refreshKey]);

  // Reset to first page when search term changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchTerm]);

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const handleAddQuestionClick = () => {
    navigate('/document-management/questioner-preparation/add');
  };

  const handleEditQuestionClick = async (question) => {

    try {
    // console.log(question,"<<<<<>>>>")

      await loadQuestionForEdit(question.preparationID, question.documentID, question.requiredQuestions, question.documentName);
      navigate(`/document-management/questioner-preparation/edit/${question.preparationID}`);
    } catch (err) {
      console.error('Error loading question for edit:', err);
      toast.error('Failed to load question for editing');
    }
  };

  const handleTooltipPosition = (event) => {
    const tooltip = event.currentTarget.querySelector(`.${styles.tooltipText}`);
    if (tooltip) {
      const rect = event.currentTarget.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      tooltip.style.top = `${rect.top + scrollTop - tooltip.offsetHeight - 10}px`;
      tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
    }
  };

  const handleDeleteClick = (question) => {

    setSelectedQuestion(question);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (selectedQuestion.questionStatus === 'Approved') {
      toast.warning('Cannot delete approved questions');
      setShowDeleteModal(false);
      setSelectedQuestion(null);
      return;
    }

    try {
      const response = await deleteQuestion(selectedQuestion.preparationID);
      if (response?.header?.errorCount === 0) {
        toast.success('Question deleted successfully');
        setShowDeleteModal(false);
        setSelectedQuestion(null);
        setRefreshKey(prev => prev + 1);
      } else {
        throw new Error(response?.header?.messages?.[0]?.messageText || 'Failed to delete question');
      }
    } catch (err) {
      console.error('Error deleting question:', err);
      toast.error(err.message || 'Failed to delete question');
    } finally {
      setShowDeleteModal(false);
      setSelectedQuestion(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedQuestion(null);
  };

  const paginate = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft':
        return styles.statusDraft;
      case 'approved':
        return styles.statusApproved;
      case 'returned':
        return styles.statusReturn;
      case 'rejected':
        return styles.statusReject;
      default:
        return styles.statusDraft;
    }
  };



  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {showDeleteModal && (
        <Modal
          title="Confirm Delete"
          message={`Are you sure you want to delete questions for document "${selectedQuestion?.documentName}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}

      <div className={styles.container}>
        <div className={styles.questionMaster}>
          <div className={styles.panelHeader}>
            <h2>Questioner Preparation</h2>
            <div className={styles.controls}>
              <div className={styles.searchContainer}>
                <FaSearch className={styles.searchIcon} />
                <input
                  type="text"
                  placeholder="Search questions..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className={styles.searchInput}
                />
                {searchTerm && (
                  <FaTimes
                    className={styles.clearIcon}
                    onClick={clearSearch}
                    title="Clear search"
                  />
                )}
                {loading && debouncedSearchTerm && (
                  <div className={styles.searchSpinner}></div>
                )}
              </div>
              <button className={styles.addUserBtn} onClick={handleAddQuestionClick}>
                + Add
              </button>
            </div>
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          <div className={styles.userTableContainer}>
            <table className={styles.userTable}>
              <thead>
                <tr>
                  <th>Document Name</th>
                  <th>Document Code</th>
                  <th>Total Questions</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="5" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : questions.length > 0 ? (
                  questions.map((question, index) => (
                    <tr key={index}>
                      <td>{question.documentName}</td>
                      <td>{question.documentCode}</td>
                      <td>{question.requiredQuestions}</td>

                      <td>
                        <div className={styles.tooltipWrapper} onMouseEnter={handleTooltipPosition}>
                          <span className={`${styles.statusBadge} ${getStatusClass(question.questionStatus)}`}>
                            {question.questionStatus}
                          </span>
                          <div className={styles.tooltipText}>
                            {question.remark || question.approvalRemarks || 'No remarks available'}
                          </div>
                        </div>
                      </td>
                      <td>


                        <div className={styles.actions}>
                          <button
                            className={`${styles.editBtn} ${question.questionStatus === 'Approved' || question.questionStatus === 'Rejected' ? styles.disabledBtn : ''}`}
                            onClick={() => handleEditQuestionClick(question)}
                            disabled={question.questionStatus === 'Rejected'}
                            title={question.questionStatus === 'Approved' || question.questionStatus === 'Rejected' ? 'Cannot edit approved or rejected questions' : 'Edit'}
                          >
                            <FaEdit className={`${styles.editIcon} ${ question.questionStatus === 'Rejected' ? styles.disabledIcon : ''}`} />
                            <span>Edit</span>
                          </button>
                          {/* <span className={styles.actionDivider}></span> */}
                          <button
                            className={styles.editBtn}
                            onClick={() => handleDeleteClick(question)}
                            disabled={false}
                            title="Delete"
                          >
                            <FcCancel className={`${styles.deleteIcon} ${question.questionStatus === 'Approved' ? styles.disabledIcon : ''}`} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="5" style={{ textAlign: 'center', padding: '20px' }}>
                      No questions found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className={styles.paginationContainer}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              paginate={paginate}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default QuestionPrepareGrid;