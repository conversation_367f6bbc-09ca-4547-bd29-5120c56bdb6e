import React, { useState, useRef, useEffect } from 'react';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { downloadFileById, FetchStreamToken } from '../../../services/DownloadService';

import { Viewer, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import mammoth from 'mammoth';

const FileViewer = ({ id, type, extension, onClose }) => {
  const [fileId, setFileId] = useState('');
  const [fileUrl, setFileUrl] = useState(null);
  const [contentType, setContentType] = useState(null);
  const [docHtml, setDocHtml] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const containerRef = useRef(null);
  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  // Cleanup function
  const cleanup = () => {
    if (fileUrl) {
      URL.revokeObjectURL(fileUrl);
    }
    setFileUrl(null);
    setContentType(null);
    setDocHtml('');
    setShowModal(false);
    setLoading(false);
  };

  useEffect(() => {
    const fetchFile = async () => {
      if (!id) return;
      setLoading(true);
      try {
        console.log(id, type, extension, onClose);
        setContentType(extension);
        if(extension === '.pdf'){
            console.log(id, type, extension, onClose);
            const response = await downloadFileById(type,id);
            const url = URL.createObjectURL(response.data);
            setFileUrl(url);
        }
        else if(extension === '.mp4'){
            const token = await FetchStreamToken(type,id);
            setFileUrl(import.meta.env.VITE_API_BASE_URL+'download/stream/'+token);
        }
        setShowModal(true);
      } catch (error) {
        console.error('Error downloading file:', error);
        alert('Failed to download the document. Please check the ID.');
        cleanup();
      } finally {
        setLoading(false);
      }
    };
    fetchFile();

    // Cleanup on unmount
    return () => {
      cleanup();
    };
  }, [id]);

  const handleCloseModal = () => {
    cleanup();
    onClose();
  };

  const renderContent = () => {
    if (loading) {
      return <div>Loading...</div>;
    }

    switch (contentType) {
      case '.pdf':
        return fileUrl ? (
          <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
            <Viewer fileUrl={fileUrl} plugins={[defaultLayoutPluginInstance]} />
          </Worker>
        ) : null;
      case '.mp4':
        return fileUrl ? (
          <video
            controls
            autoPlay
            style={{ width: '100%', height: '100%', objectFit: 'contain' }}
          >
            <source src={fileUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : null;
      default:
        return (
          <div style={{ padding: '20px', color: '#fff', fontSize: '18px' }}>
            Unsupported file type: <strong>{contentType}</strong>
          </div>
        );
    }
  };

  return (
    <>
      {showModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 9999,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <div
            ref={containerRef}
            style={{
              width: '90%',
              height: '90%',
              backgroundColor:
                contentType === 'application/pdf' || contentType?.includes('word')
                  ? '#fff'
                  : '#000',
              borderRadius: '8px',
              overflow: 'hidden',
              position: 'relative',
              boxShadow: '0 0 20px rgba(0,0,0,0.5)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: contentType?.includes('word') ? '20px' : 0,
            }}
          >
            <button
              onClick={handleCloseModal}
              style={{
                position: 'absolute',
                top: '0px',
                right: '0px',
                zIndex: 10000,
                backgroundColor: '#f44336',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                padding: '8px 16px',
                cursor: 'pointer',
              }}
            >
              Close
            </button>

            {renderContent()}
          </div>
        </div>
      )}
    </>
  );
};

export default FileViewer;
 