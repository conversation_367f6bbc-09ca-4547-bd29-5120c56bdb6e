.navbar {
  position: fixed;
  top: 0;
  left: 16rem;
  height: 8vh;
  width: calc(100% - 16rem);
  background-color: #F1F1F2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  z-index: 50;
  box-shadow: 0 10px 6px -1px rgba(0, 0, 0, 0.05);
}

.navLeft {
  font-weight: bold;
  color: #333; /* Darker text for light bg */
  font-size: 1.25rem;
}

.navRight {
  display: flex;
  align-items: center;
  position: relative;
}

.sessionAndInfo {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-right: 0.75rem;
}

.sessionBlock {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.plantName {
  color: #222;
  font-size: 0.75rem;
  margin-bottom: 2px;
  text-align: center;
  width: 100%; /* ensures centering works */
  display: flex;
  justify-content: center;
  align-items: center;
}

.sessionAndInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-right: 0.75rem;
}

.sessionTime {
  font-size: 0.75rem;
  color: #444; /* Dark gray for readability */
  white-space: nowrap;
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.userName {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111; /* Near-black for boldness */
  margin: 0;
}

.userRole {
  font-size: 0.75rem;
  color: #555; /* Muted dark text */
  margin: 0;
}

.navAvatar {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  cursor: pointer;
}

.dropdown {
  position: absolute;
  top: 3rem;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  width: 10rem;
  padding: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.dropdownItem {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  width: 100%;
  text-align: center;
  background: #ffffff;
  color: #111827;
  cursor: pointer;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.dropdownItem:hover {
  background-color: #f0f0f0;
}

/* Responsive Navbar */
@media (max-width: 768px) {
  .navbar {
    left: 0;
    width: 100%;
    padding: 0 0.75rem;
  }

  .sessionAndInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    margin-right: 0.5rem;
  }

  .userInfo {
    align-items: flex-start;
  }

  .userName,
  .userRole,
  .sessionTime {
    font-size: 0.75rem;
  }
}