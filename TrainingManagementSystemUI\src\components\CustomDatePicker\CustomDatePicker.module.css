.datePickerContainer {
  position: relative;
  width: 100%;
}

.dateInput {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  cursor: pointer;
  background-color: white;
  min-height: 36px;
}

.placeholder {
  color: #666;
  font-size: 0.9rem;
}

.selectedDate {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
}

.calendarIcon {
  font-size: 1rem;
  transition: transform 0.2s;
}

.calendarIcon.active {
  transform: scale(1.1);
}

.calendar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 12px;
  width: 280px;
}

.calendarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 4px;
}

.calendarHeader button {
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  color: #127C96;
  padding: 4px 8px;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendarHeader button:hover {
  background-color: rgba(18, 124, 150, 0.1);
}

.calendarHeader span {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.weekDays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 6px;
}

.weekDay {
  text-align: center;
  font-size: 0.7rem;
  color: #666;
  padding: 2px;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.day {
  text-align: center;
  padding: 6px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 0.85rem;
  position: relative;
}

.day:hover:not(.disabled) {
  background-color: rgba(18, 124, 150, 0.1);
  color: #127C96;
}

.day.selected {
  background-color: #127C96;
  color: white;
}

.day.future {
  color: #127C96;
  font-weight: 500;
}

.day.past {
  color: #333;
  font-weight: 500;
}

.day.future::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #127C96;
  border-radius: 50%;
}

.day.past::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #333;
  border-radius: 50%;
}

.day.disabled {
  color: #ccc;
  cursor: not-allowed;
  opacity: 0.5;
}

.emptySlot {
  padding: 6px;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
} 