import React from 'react';
import styles from './coursecodeView.module.css';

const CoursecodeView = ({ courseCodeDetails, onClose }) => {
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.viewModal}>
        <div className={styles.modalHeader}>
          <h2> Course Code : 
            <span className={styles.titleText}>{courseCodeDetails.courseTitle}</span>
            <span className={styles.codeText}>({courseCodeDetails.courseCode})</span>
          </h2>
          <button 
            className={styles.closeButton}
            onClick={onClose}
          >
            ×
          </button>
        </div>
        <div className={styles.viewModalContent}>
          <div className={styles.detailBox}>
            <h3>Department</h3>
            <span>:</span>
            <p>{courseCodeDetails.departmentName}</p>
          </div>
          <div className={styles.detailBox}>
            <h3>Status</h3>
            <span>:</span>
            <p>{courseCodeDetails.courseCodeStatus}</p>
          </div>
        </div>

        <div className={styles.linkedDocumentsSection}>
          <h3>Linked Documents</h3>
          {Array.isArray(courseCodeDetails.linkedDocuments) && courseCodeDetails.linkedDocuments.length > 0 ? (
            <div className={styles.linkedDocumentsContainer}>
              {Array.from(new Set(courseCodeDetails.linkedDocuments.map(doc => doc.type))).map(type => (
                <div key={type} className={styles.documentSection}>
                  <h4>{type}</h4>
                  {courseCodeDetails.linkedDocuments.filter(doc => doc.type === type).length > 0 ? (
                    <ul className={styles.linkedDocumentsList}>
                      {courseCodeDetails.linkedDocuments
                        .filter(doc => doc.type === type)
                        .map((doc, idx) => (
                          <li key={idx} className={styles.documentItem}>
                            <div className={styles.documentInfo}>
                              <div className={styles.documentName}>
                                {doc.name} <span className={styles.documentCode}>({doc.code})</span>
                              </div>
                              <span className={styles.documentFrequency}>{doc.frequency}</span>
                            </div>
                          </li>
                        ))}
                    </ul>
                  ) : (
                    <p className={styles.noDocuments}>No {type} documents linked.</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className={styles.noDocuments}>No linked documents.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default CoursecodeView; 