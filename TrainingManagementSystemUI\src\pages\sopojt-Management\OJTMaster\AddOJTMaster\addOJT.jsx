import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './addOJT.module.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { createOJT } from '../../../../services/sopojt-Management/OJTMasterService'; // Corrected import path
import { fetchEvaluationTypes } from '../../../../services/lookup/lookupService'; // Adjust the path as needed

const AddOJTMaster = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    ojtCode: '',
    ojtTitle: '',
    activitySteps: '',
    activityDetails: [],
    associatedDocument: null,
    associatedDocumentExtension: '',
    associatedDocumentSize: '',
    evaluationType: '',
    numberOfSets: '',
    expectedDuration: '',
    remarks: '',
  });

  const [expectedDurationHours, setExpectedDurationHours] = useState('');
  const [expectedDurationMinutes, setExpectedDurationMinutes] = useState('');

  const [evaluationOptions, setEvaluationOptions] = useState([]);

  useEffect(() => {
    const loadEvaluationTypes = async () => {
      try {
        const res = await fetchEvaluationTypes();
        if (res?.evaluationTypes) {
          setEvaluationOptions(res.evaluationTypes);
        }
      } catch (err) {
        console.error("Failed to fetch evaluation types:", err);
      }
    };

    loadEvaluationTypes();
  }, []);
  useEffect(() => {
    const steps = parseInt(formData.activitySteps);
    const currentDetailsCount = formData.activityDetails.length;

    if (!isNaN(steps) && steps > 0) {
      if (steps > currentDetailsCount) {
        // Add new empty fields if steps increases
        setFormData(prev => ({
          ...prev,
          activityDetails: [
            ...prev.activityDetails,
            ...Array(steps - currentDetailsCount).fill('')
          ]
        }));
      }
      // If steps decreases or stays the same (but > 0), do nothing to the array size.
      // The rendering logic will handle showing delete buttons for excess fields.
    } else if (currentDetailsCount > 0 && (isNaN(steps) || steps <= 0)) {
        // If steps becomes invalid or <= 0, but there are existing details, keep them.
        // The delete button logic will handle showing delete buttons for all fields.
        console.log("Activity steps cleared or invalid, but keeping existing details.");
    } else if (currentDetailsCount === 0 && (isNaN(steps) || steps <= 0)) {
        // If steps becomes invalid or <= 0 and there are no existing details, ensure array is empty.
        setFormData(prev => ({ ...prev, activityDetails: [] }));
    }
  }, [formData.activitySteps]); // Depend only on activitySteps to trigger this effect

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'activitySteps' || name === 'numberOfSets') {
      setFormData({ ...formData, [name]: value.replace(/[^0-9]/g, '') });
    } else if (name === 'expectedDurationHours') {
      setExpectedDurationHours(value.replace(/[^0-9]/g, ''));
    } else if (name === 'expectedDurationMinutes') {
      setExpectedDurationMinutes(value.replace(/[^0-9]/g, ''));
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleActivityDetailChange = (index, value) => {
    const updatedDetails = [...formData.activityDetails];
    updatedDetails[index] = value;
    setFormData({ ...formData, activityDetails: updatedDetails });
  };

  const handleRemoveActivityDetail = (indexToRemove) => {
      setFormData(prev => ({
          ...prev,
          activityDetails: prev.activityDetails.filter((_, index) => index !== indexToRemove)
      }));
       // Note: This will remove the field permanently, not just hide the delete button.
       // If you want to keep the data hidden but potentially restore it, a more complex state would be needed.
       // For this request, permanent removal on delete button click seems intended.
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData({ 
        ...formData,
        associatedDocument: file,
        associatedDocumentExtension: file.name.split('.').pop() || '',
        associatedDocumentSize: `${(file.size / 1024).toFixed(2)} KB` // Convert bytes to KB
      });
    } else {
       setFormData({ 
        ...formData,
        associatedDocument: null,
        associatedDocumentExtension: '',
        associatedDocumentSize: '',
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Add validation logic here if not already covered by 'required'

    if (formData.activityDetails.length !== parseInt(formData.activitySteps)) {
      toast.error('Number of Activity Details must match the Activity Steps count.');
      return;
    }

    const userID = sessionStorage.getItem('userID') || '0'; // Get userID from session storage
    const createdBy = sessionStorage.getItem('userName') || 'system'; // Get user name from session storage or use default

    console.log('Form submitted for creation:', formData);

    try {
      const totalMinutes = (parseInt(expectedDurationHours) || 0) * 60 + (parseInt(expectedDurationMinutes) || 0);

      // Call the createOJT API function
      const res = await createOJT({
          ...formData,
          activitySteps: formData.activityDetails.length,
          expectedDuration: totalMinutes
      }, formData.associatedDocument, userID, createdBy);

      if (res?.header?.errorCount === 0) {
        toast.success(res.header.messages?.[0]?.messageText || 'OJT Master added successfully!');
        // Add a delay before navigating to allow the toast to be seen
        setTimeout(() => {
          navigate('/document-management/ojt-master'); // Redirect after success
        }, 1500); // Delay for 1.5 seconds
      } else {
        toast.error(res?.header?.messages?.[0]?.messageText || 'Failed to add OJT Master.');
        // Optionally add a delay before navigating on error as well
        // setTimeout(() => {
        //   navigate('/sopojt-management/ojt-master'); // Redirect even on error
        // }, 1500); // Delay for 1.5 seconds
      }
    } catch (error) {
      console.error('Error creating OJT Master:', error);
      toast.error('An error occurred while adding OJT Master.');
      // Optionally add a delay before navigating on error as well
      // setTimeout(() => {
      //   navigate('/sopojt-management/ojt-master'); // Redirect even on error
      // }, 1500); // Delay for 1.5 seconds
    }
  };

  const handleCancel = () => {
    navigate('/document-management/ojt-master'); // Navigate back to OJT Master grid
  };

  const activityStepsCount = parseInt(formData.activitySteps) || 0;

  return (
    <div className={styles.container}>
      <ToastContainer />
      <div className={styles.formCard}>
        <h2 className={styles.sectionHeading}>On Job Training Master</h2>
        <form onSubmit={handleSubmit}>
          <div className={styles.formSection}>
            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>OJT Training Code <span className={styles.required}>*</span></div>
              <div className={styles.inputCell}>
                <input
                  type="text"
                  name="ojtCode"
                  value={formData.ojtCode}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>OJT Training Title <span className={styles.required}>*</span></div>
              <div className={styles.inputCell}>
                <input
                  type="text"
                  name="ojtTitle"
                  value={formData.ojtTitle}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>Activity Steps <span className={styles.required}>*</span></div>
              <div className={styles.inputCell}>
                <input
                  type="text"
                  name="activitySteps"
                  value={formData.activitySteps}
                  onChange={handleInputChange}
                  required
                />
                 {formData.activitySteps !== '' && activityStepsCount <= 0 && ( // Validation message for steps
                    <span className={styles.validationError}>Activity Steps must be a positive number</span>
                 )}
              </div>
            </div>

            {/* Dynamic Activity Details Inputs */}
            {formData.activityDetails.length > 0 && (
              <div className={styles.activityDetailsContainer}>
                <h3>Activity Details</h3>
                {formData.activityDetails.map((detail, index) => (
                  <div key={index} className={styles.activityDetailRow}>
                    <div className={styles.fieldLabel}>
                      Step {index + 1} <span className={styles.required}>*</span>
                    </div>
                    <div className={styles.inputCell}>
                      <textarea
                        value={detail}
                        onChange={(e) => handleActivityDetailChange(index, e.target.value)}
                        required
                      />
                       {/* Show delete button if current index is >= the specified number of steps */}
                      {formData.activityDetails.length !== activityStepsCount && (
                         <button 
                            type="button" 
                            onClick={() => handleRemoveActivityDetail(index)}
                            className={styles.removeActivityDetailButton}
                         >
                           Delete
                         </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>Associated Document</div>
              <div className={styles.inputCell}>
                <input
                  type="file"
                  name="associatedDocument"
                  onChange={handleFileChange}
                  accept=".pdf,.ppt,.pptx,.mp4"
                />
                <div className={styles.fileInfoText}>Accepted formats: PDF, PPT, PPTX, MP4 (Max size: 500MB)</div>
                {formData.associatedDocument && (
                   <div className={styles.fileDetails}>
                       <p>File: {formData.associatedDocument.name}</p>
                       <p>Type: {formData.associatedDocumentExtension}</p>
                       <p>Size: {formData.associatedDocumentSize}</p>
                   </div>
                )}
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>Evaluation Type <span className={styles.required}>*</span></div>
              <div className={styles.inputCell}>
                <select
                  name="evaluationType"
                  value={formData.evaluationType}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select Evaluation Type</option>
                  {evaluationOptions.map(type => (
                    <option key={type.evaluationTypeId} value={type.evaluationTypeName}>
                      {type.evaluationDisplayName}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>Number of Set <span className={styles.required}>*</span></div>
              <div className={styles.inputCell}>
                <input
                  type="text"
                  name="numberOfSets"
                  value={formData.numberOfSets}
                  onChange={handleInputChange}
                  required
                />
                 {formData.numberOfSets !== '' && parseInt(formData.numberOfSets) <= 0 && (
                    <span className={styles.validationError}>Number of Sets must be greater than 0</span>
                 )}
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>Expected Duration</div>
              <div className={styles.inputCell} style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <input
                  type="text"
                  name="expectedDurationHours"
                  value={expectedDurationHours}
                  onChange={handleInputChange}
                  placeholder="hrs"
                  className={styles.durationInput}
                  maxLength={2}
                />
                <span className={styles.durationLabel}>hour</span>
                <input
                  type="text"
                  name="expectedDurationMinutes"
                  value={expectedDurationMinutes}
                  onChange={handleInputChange}
                  placeholder="mins"
                  className={styles.durationInput}
                  maxLength={2}
                />
                <span className={styles.durationLabel}>min</span>
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.fieldLabel}>Remarks</div>
              <div className={styles.inputCell}>
                <textarea
                  name="remarks"
                  value={formData.remarks}
                  onChange={handleInputChange}
                />
              </div>
            </div>

          </div> {/* End of formSection */}

          <div className={styles.buttonContainer}>
            <button type="submit" className={styles.actionButton}>Submit</button>
            <button type="button" className={styles.actionButtonCancel} onClick={handleCancel}>Cancel</button>
          </div>
        </form>
      </div> {/* End of formCard */}
    </div> /* End of container */
  );
};

export default AddOJTMaster;
