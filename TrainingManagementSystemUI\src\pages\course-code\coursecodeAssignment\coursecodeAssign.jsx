import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaBan } from 'react-icons/fa';
import styles from './coursecodeAssign.module.css'; // Updated CSS import
import Pagination from '../../../components/pagination/Pagination';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';
import { fetchCourseCodeAssignmentsByUserId, disableCourseCodeAssignment } from '../../../services/course-code/CoursecodeAssignmentService'; // Updated service import

const CoursecodeAssign = () => { // Updated component name
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [coursecodeData, setCoursecodeData] = useState([]); // Updated state name
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCoursecode, setSelectedCoursecode] = useState(null); // Updated state name
  const tableContainerRef = useRef(null);
  const itemsPerPage = 10;
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 2000);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm,
          showOnlyUnderReview: false
        };
        const res = await fetchCourseCodeAssignmentsByUserId(payload);
        setCoursecodeData(res.coursecodes || []);
        setTotalRecords(res.totalRecord || 0);
      } catch {
        setCoursecodeData([]); // Updated state name
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  const checkScroll = () => {
    if (tableContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = tableContainerRef.current;
      const isAtStart = scrollLeft <= 0;
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 1;
    }
  };

  // useEffect(() => {
  //   const container = tableContainerRef.current;
  //   if (container) {
  //     checkScroll();
  //     container.addEventListener('scroll', checkScroll);
  //     window.addEventListener('resize', checkScroll);
  //   }
  //   return () => {
  //     if (container) {
  //       container.removeEventListener('scroll', checkScroll);
  //       window.removeEventListener('resize', checkScroll);
  //     }
  //   };
  // }, []);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1);
  };



  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleEdit = (courseCode) => {
    // Navigate directly to edit page - the Edit component will fetch data via API
    navigate(`/course-code/course-code-assignment/edit/${courseCode.courseCodeID}`);
  };

  const handleDeactivate = (coursecode) => {
    setSelectedCoursecode(coursecode);
    setShowDeleteModal(true);
  };

  const confirmDisable = async () => {
    if (selectedCoursecode) {
      try {
        const result = await disableCourseCodeAssignment(selectedCoursecode.courseCodeID);

        if (result?.header?.errorCount === 0) {
          toast.success(result.header.messages[0].messageText);
          // Refresh the data
          const userID = sessionStorage.getItem('userID');
          const payload = {
            userID: userID ? parseInt(userID) : 0,
            page: {
              offset: (currentPage - 1) * itemsPerPage,
              fetch: itemsPerPage
            },
            searchText: debouncedSearchTerm,
            showOnlyUnderReview: false
          };
          const fetchRes = await fetchCourseCodeAssignmentsByUserId(payload);
          setCoursecodeData(fetchRes.coursecodes || []);
          setTotalRecords(fetchRes.totalRecord || 0);
        } else {
          toast.error(result?.header?.messages[0]?.messageText || 'Failed to disable course code assignment');
        }
      } catch (error) {
        toast.error('Error disabling course code assignment');
      } finally {
        setShowDeleteModal(false);
        setSelectedCoursecode(null);
      }
    }
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      {showViewModal && (
        <Modal
          title="View Course Code Details" // Updated modal title
          message={
            <div className={styles.viewModalContent}>
              <div className={styles.viewDetails}>
                {/* Adjust these based on actual Course Code data structure */}
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Title:</span>
                  <span className={styles.detailValue}>
                    {selectedCoursecode?.courseTitle}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Course Code:</span>
                  <span className={styles.detailValue}>
                    {selectedCoursecode?.courseCode}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Department ID:</span>
                  <span className={styles.detailValue}>
                    {selectedCoursecode?.departmentID || 'N/A'}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Assignment Count:</span>
                  <span className={styles.detailValue}>
                    {selectedCoursecode?.assignmentCount || 0}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Course Code Status:</span>
                  <span className={styles.detailValue}>
                    {selectedCoursecode?.courseCodeStatus || 'N/A'}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Remarks:</span>
                  <span className={styles.detailValue}>
                    {selectedCoursecode?.remarks || 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          }
          onConfirm={() => setShowViewModal(false)}
          onCancel={() => {
            setShowViewModal(false);
            setSelectedCoursecode(null); // Updated state name
          }}
          showCancelButton={false}
          confirmText="Close"
        />
      )}

      {showDeleteModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate the course code assignment "${selectedCoursecode?.courseTitle}"?`}
          onConfirm={confirmDisable}
          onCancel={() => {
            setShowDeleteModal(false);
            setSelectedCoursecode(null);
          }}
          confirmText="Deactivate"
          cancelText="Cancel"
        />
      )}

      <div className={styles.container}>
        <div className={styles.documentReview}>
          <div className={styles.panelHeader}>
            <h2>Course Code Assignment</h2> {/* Updated title */}
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={handleSearch}
                className={styles.searchInput}
              />
              <button
                className={styles.addDocBtn}
                onClick={() => navigate('/course-code/course-code-assignment/add')}
                title="Add Assignment"
              >
                + Add
              </button>
            </div>
          </div>

          <div ref={tableContainerRef} className={styles.docTableContainer}>
            <div className={styles.tableWrapper}>
              <table className={styles.docTable}>
                <thead>
                  <tr>
                    <th>Course Code</th>
                    <th>Course Code Title</th>
                    <th>Number of Assignments</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="4" className={styles.spinnerCell}>
                        {" "}
                        {/* Updated colspan */}
                        <div className={styles.spinner}></div>
                      </td>
                    </tr>
                  ) : coursecodeData.length > 0 ? (
                    coursecodeData.map(
                      (item) => (
                        <tr key={item.courseCodeID}>
                          <td>{item.courseCode}</td>
                          <td>{item.courseTitle}</td>
                          <td>{item.assignmentCount || 0}</td>
                          <td>
                            <div className={styles.actions}>
                              <button
                                className={styles.actionButton}
                                onClick={() => handleEdit(item)}
                                title="Edit"
                              >
                                <FaEdit className={styles.editIcon} />
                                <span>Edit</span>
                              </button>
                              <button
                                className={styles.actionButton}
                                onClick={() => handleDeactivate(item)}
                                title="Disable"
                              >
                                <FaBan className={styles.deactivateIcon} />
                                <span>Disable</span>
                              </button>
                            </div>
                          </td>
                        </tr>
                      )
                    )
                  ) : (
                    <tr>
                      <td
                        colSpan="4"
                        style={{ textAlign: "center", padding: "20px" }}
                      >
                        {" "}
                        {/* Updated colspan */}
                        No Course Code records found. {/* Updated message */}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(totalRecords / itemsPerPage)}
            paginate={handlePageChange}
          />
        </div>
      </div>
    </>
  );
};

export default CoursecodeAssign; // Updated component name
