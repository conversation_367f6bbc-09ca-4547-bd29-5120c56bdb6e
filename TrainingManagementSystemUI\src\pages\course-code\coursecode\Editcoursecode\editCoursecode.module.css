.container {
  padding: 1rem;
  height: calc(100vh - 5rem);
  width: 100vw;
  padding-top: 5rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.formCard {
  max-width: 1800px; /* Adjusted max width */
  margin-left: 16rem;
  margin-right: auto;
  background: #fff;
  padding: 2.5rem 2.5rem; /* Adjusted padding */
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 2rem; /* Increased gap between sections */
  color: #333;
}

.formTitle {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.formSection {
  background: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 6px rgba(0,0,0,0.05);
  border: 1px solid #e9ecef;
}

.formSection h3 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

.formRowLayout {
  display: flex;
  gap: 2rem; /* Gap between columns */
  margin-bottom: 1rem;
}

.formColumn {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem; /* Gap between rows within a column */
}

.formRow {
  display: flex;
  align-items: center;
  gap: 1rem; /* Space between label and input */
}

.fieldLabel {
  width: 150px;
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  flex-shrink: 0;
}

.required {
  color: #e53935;
  margin-left: 2px;
  font-weight: bold;
}

.inputCell {
  flex: 1;
  display: flex;
  align-items: center;
}

input[type="text"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 0.6rem 0.75rem; /* Adjusted padding */
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background: #fff;
  color: #495057;
  box-sizing: border-box;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

input[type="text"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea {
  resize: vertical;
  min-height: 80px; /* Adjusted minimum height */
}

/* Document and OJT Selection Section */
.documentOjtSection {
  margin-top: 1.5rem; /* Space above this section */
}

.documentOjtLayout {
  display: flex;
  gap: 2rem; /* Gap between the two columns */
  margin-top: 1rem;
}

.availableOjtColumn {
  flex: 0 0 30%; /* Flex-grow: 0, Flex-shrink: 0, Flex-basis: 40% */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 500px;
}

.selectedOjtColumn {
  flex: 0 0 70%; /* Flex-grow: 0, Flex-shrink: 0, Flex-basis: 60% */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 500px;
}

.layoutDivider {
  width: 1px;
  background-color: #dee2e6; /* Light grey divider */
  flex-shrink: 0; /* Prevent shrinking */
}

.documentOjtSearch input[type="text"] {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
  box-sizing: border-box;
}

.documentOjtList {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  max-height: 600px;
  overflow-y: auto;
  background-color: #fff;
  padding: 0.5rem;
  position: relative;
}

.loadingIndicator {
  text-align: center;
  padding: 10px;
  color: #6c757d;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #e9ecef;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
}

.documentDetails {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.documentMeta {
  color: #666;
  font-size: 14px;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.documentType {
  color: #666;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid #e9ecef;
  display: inline-flex;
  align-items: center;
}

.documentCode {
  color: #666;
  font-size: 12px;
  background-color: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid #e9ecef;
  display: inline-flex;
  align-items: center;
}

.documentName {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 2px 0;
}

.documentOjtItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  gap: 12px;
}

.documentOjtItem:last-child {
  border-bottom: none;
}

.documentOjtItem:hover {
  background-color: #f8f9fa;
}

.addItemButton {
  background: none;
  border: none;
  color: #28a745;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.noResults,
.noSelected {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-size: 0.95rem;
}

.selectedItemsContainerheadings {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Gap between bubbles */
  margin-top: 0.5rem;
  margin-right: 0.5rem;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  max-height: 600px;
  overflow-y: hidden;
  width: 90%; /* Ensure container takes full width of its parent column */
  box-sizing: border-box;
}

.selectedItemsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Gap between bubbles */
  margin-top: 0.5rem;
  margin-right: 0.5rem;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  max-height: 600px;
  overflow-y: auto;
  width: 90%; /* Ensure container takes full width of its parent column */
  box-sizing: border-box; /* Include padding and border */
}

.selectedItemsContainer h4 {
  overflow-y: hidden;
}

.selectedItemBubble {
  background-color: #fff;
  color: #333;
  padding: 6px 10px; /* Adjusted padding */
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px; /* Gap between remove button, label, and frequency options */
  font-size: 0.85rem;
  border: 1px solid #becbd2;
  flex-grow: 0; /* Prevent bubble from growing */
  flex-shrink: 0; /* Allow shrinking but prefer not to */
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
}

.selectedItemBubble .itemLabel {
  flex-grow: 0;
  flex-shrink: 1; /* Allow label to shrink */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* Keep text on a single line */
}

.removeItemButton {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-weight: bold;
  padding-left: 15px; /* Remove padding to rely on gap */
  font-size: 1rem;
  transition: color 0.2s ease;
  flex-shrink: 0; /* Prevent button from shrinking */
  order: -1; /* Place at the beginning of the flex container */
}

.removeItemButton:hover {
  color: #c82333;
}

.frequencyOptions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 0;
  padding: 0;
  background: none;
  border: none;
  align-items: center;
  flex-shrink: 0;
}

.frequencyToggle {
  padding: 4px 12px;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  background: white;
  color: #495057;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.frequencyToggle:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.frequencyToggle.selected {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.frequencyToggle.selected:hover {
  background: #138496;
  border-color: #138496;
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.actionButton {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButton:hover {
  background-color: #0f6a83;
}

.actionButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Add cancel button style */
.cancelButton {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.cancelButton:hover {
  background-color: #cccccc;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.error {
  color: #f44336;
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

/* Department Group Styles */
.departmentGroup {
  margin-bottom: 8px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.departmentHeader {
  width: 100%;
  padding: 10px 12px;
  background: #f8f9fa;
  border: none;
  text-align: left;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.departmentHeader:hover {
  background: #e9ecef;
}

.expandIcon {
  font-size: 0.8rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.departmentDocs {
  background: white;
  border-top: 1px solid #e9ecef;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .formCard {
    padding: 1rem 0.8rem;
    margin-left: 0;
    gap: 1rem;
  }

  .formRowLayout {
    flex-direction: column;
    gap: 1rem;
  }

  .formColumn {
    gap: 0.6rem;
  }

  .formRow {
    flex-direction: column;
    gap: 0.4rem;
    align-items: flex-start;
  }

  .fieldLabel {
    width: 100%;
    padding-right: 0;
  }

  .inputCell {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }

  .documentOjtLayout {
    flex-direction: column;
    gap: 1.5rem;
  }

  .layoutDivider {
    width: 100%;
    height: 1px;
  }

  .buttonContainer {
    flex-direction: column;
    gap: 0.8rem;
  }

  .actionButton {
    width: 100%;
  }

  .selectedItemBubble {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .frequencyOptions {
    justify-content: center;
    width: 100%;
  }
}
