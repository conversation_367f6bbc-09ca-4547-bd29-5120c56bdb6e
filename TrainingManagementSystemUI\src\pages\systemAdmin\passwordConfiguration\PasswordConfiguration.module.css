.container {
  position: absolute;
  top: 6rem;
  left: 16rem;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 40px 40px 40px; /* Increased left and right padding */
  box-sizing: border-box;
  height: calc(100vh - 6rem);
  width: calc(100% - 16rem);
  background-color: #ffffff;
  color: #000000;
  overflow-y: auto; /* Enable scroll effect only for the container */
}

.heading {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #127C96;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 50px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form {
  margin: 0 auto; /* Center the form horizontally */
  margin-top: 5rem;
  background: #ffffff;
  padding: 5% 10% 5% 10%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  width: 90%; /* Form width is still 90% of the container */
  max-width: 100%;
}

.field {
  display: flex;
  align-items: left;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  color: #000000;
}

.field label {
  font-weight: 500;
  width: 60%;
}

.field input[type='number'] {
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
  width: 35%;
  text-align: center;
}

.desc {
  font-size: 0.65rem;
  color: #555;
  margin-left: 4px;
}


.radiobuttons {
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1.2rem;
  width: auto;
  position: relative;
  margin-left: 20px;
}

.radiobuttons label {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.9rem;
  white-space: nowrap;
  position: relative;
  cursor: pointer;
}

.radiobuttons label::after {
  content: attr(data-hover-text);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(18, 124, 150, 0.95);
  color: white;
  padding: 0.4rem 0.7rem;
  border-radius: 8px;
  font-size: 0.75rem;
  margin-left: 0.5rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 10;
}


.radiobuttons input[type='radio'] {
  all: unset; /* Completely resets all browser styles */
  width: 20px;
  height: 20px;
  border: 2px solid #006666;
  border-radius: 50%;
  background-color: white;
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
}

/* Custom checkmark when selected */
.radiobuttons input[type='radio']:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -58%);
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

/* Change background when selected */
.radiobuttons input[type='radio']:checked {
  background-color: #006666;
  border-color: #009898;
  color: #127C96;
}

/* Focus style */
.radiobuttons input[type='radio']:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 102, 102, 0.3);
  color: #127C96;
}
.radioWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.radioDesc {
  font-size: 0.65rem;
  color: #555;
  margin-left: 1.6rem; /* aligns with radio button text */
  margin-top: 0.1rem;
}


.submitBtn {
  margin-top: 1.5rem;
  padding: 0.7rem 1.5rem;
  background-color: #1690ae;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submitBtn:hover {
  background-color: #004d4d;
}

.field input:focus {
  box-sizing: border-box;

  border-color: #006666;
  outline: none;
}

@media (max-width: 768px) {
  .form {
    width: 90%;
    max-width: 100%;
    padding: 1.5rem;
  }

  .field input[type='number'],
  .field input[type='text'],
  .field input[type='email'] {
    width: 100%; /* Make input fields take full width */
  }

  .field label {
    width: 40%; /* Adjust label width */
  }

  .submitBtn {
    width: 100%; /* Full width button */
  }
}
.errorInput {
  border: 1px solid red;
}

.errorText {
  color: red;
  font-size: 0.8rem;
  margin-top: 4px;
}


@media (max-width: 480px) {
  .form {
    padding: 1rem;
  }

  .field input[type='number'],
  .field input[type='text'],
  .field input[type='email'] {
    width: 100%;
  }

  .submitBtn {
    padding: 1rem;
  }
}